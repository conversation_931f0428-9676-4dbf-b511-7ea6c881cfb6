<?php

return [

  'portals' => [
    'trav' => [
      'https://traveloplex.ca',
      'https://traveloplex.com',
    ],
    'travel' => [
      'https://b2bportal.traveloplex.com'
    ],
  ],
  "fetch_portal_config_data_url" => "https://booking.traveleplex.com/appcache_files/portal_config/",

  'route_blocking_data_path' => 'upload/route_blocking',
  'route_blocking_upload_engineapi' => 'true',
  'route_blocking_combination_count' => 5000,

  'ce_api_endpoint' => 'https://api.cttsid.com/AirShopping',
  'ce_api_data' => [
    'CA' => ['authKey' => '5c50b4df4b176845cd235b6a510c69031685717831', 'rSource' => 'bmtgoogle1', 'deepLink' => 'https://crm.tarveloplex-uat.vibrace.com.ca', 'resultLink' => 'https://crm.traveloplex-uat.vibrace.com.ca/flightresult/'],
    'US' => ['authKey' => '192a97ff0f5454271b0a6527cee69ba11659246700', 'rSource' => 'bmtusgflights1', 'deepLink' => 'https://crm.tarveloplex-uat.vibrace.com', 'resultLink' => 'https://crm.traveloplex-uat.vibrace.com/flightresult/'],
    'GB' => ['authKey' => '192a97ff0f5454271b0a6527cee69ba11659246700', 'rSource' => 'bmtgbgflights1', 'deepLink' => 'https://crm.tarveloplex-uat.vibrace.com', 'resultLink' => 'https://crm.traveloplex-uat.vibrace.com/flightresult/']
  ],

  'airlabs' => [
    'endpoint'  => 'https://airlabs.co/api/v9/',
    'api_session_key' => '86d3d197-aeeb-4ee5-8784-5a2276f25a0d',  //'fa2f74cb-c93d-4821-8eab-e64adf6de88a', //'37c0d047-d922-4ca2-b36b-2368bc9d362d', // '7800db2e-0137-4f12-988c-c16800271535'
    'time'    => 2,
  ],

  'aviation' => [
    'endpoint'  => 'https://aviation-edge.com/v2/',
    'api_session_key' => '5e9afb-16d51a',
    'time'    => 2,
  ],

  'fohelp' => [
    'admin_username' => env('FOHELP_ADMIN_USERNAME', '<EMAIL>'),
    'admin_password' => env('FOHELP_ADMIN_PASSWORD', 'secret'),
    'api_endpoints' => [
      'register' => env('FOHELP_API_REGISTER', 'http://localhost/faveo_24mar/public/api/v1/helpdesk/register'),
      'authenticate' => env('FOHELP_API_AUTHENTICATE', 'http://localhost/faveo_24mar/public/api/v1/authenticate'),
      'login' => env('FOHELP_API_LOGIN', 'http://localhost/faveo_24mar/public/postLogin'),
      'ticketsbyuser' => env('FOHELP_API_TICKETSBYUSER', 'http://localhost/faveo_24mar/public/api/v1/helpdesk/my-tickets-user')
    ],

  ],

  'rest_api' => [
    'api_token_session_timeout' => '5', //minutes

    'api_endpoints' => [
      'rest' => env('BACKEND_API_REGISTER', 'http://localhost/metaapi_24apr_api/public/api/rest'),
      'register' => env('BACKEND_API_REGISTER', 'http://localhost/metaapi_24apr_api/public/api/authenticate'),
      'authenticate' => env('BACKEND_API_AUTHENTICATE', 'http://localhost/metaapi_24apr_api/public/api/authenticate'),
      'login' => env('BACKEND_API_LOGIN', 'http://localhost/metaapi_24apr_api/public/api/authenticate'),
      'ticketsbyuser' => env('BACKEND_API_TICKETSBYUSER', 'http://localhost/metaapi_24apr_api/public/api/authenticate')
    ],
  ],


  'sabre_soap' => [
    'username'  => '3169FSDF79',
    'password'    => 'FSDFSDFS',
    'pcc'   => 'WP32',


    'domain'      => 'AA',
    'dk_number' => '**********',
    'end_trans_given_name' => 'Skyroute',
    'endpoint' => 'https://webservices.sabre.com',
    'agency_address' => ['line1' => '106-1200 Markham Road, Scarborough', 'line2' => 'Ontario M1H3C3,Canada', 'city' => 'Scarborough', 'state' => 'Ontario', 'pincode' => 'M1H 3C3', 'country' => 'CA'],
    'airline_hosting_sabre' => ['3CFD' => [], '6KHG' => ['AA'], 'KY6G' => ['AA']],
  ],
  'flights_time_source' => 'sabre_soap',
  'price_threshold' => [
    'price_comparision_method' => env('PRICE_THRESHOLD_METHOD', 'perc'), //amount/perc
    'price_comparision_threshold' => env('GFS_PRICE_DIFFERENCE_THRESHOLD', 200), //
    'price_comparision_threshold_percentage' => env('GFS_PRICE_DIFFERENCE_THRESHOLD_PERCENTAGE', 8),
    'price_comparision_upperlimit_percentage' => env('GFS_PRICE_DIFFERENCE_UPPERLIMIT_PERCENTAGE', 10),
    'show_priced_fare_only' => env('SHOW_PRICED_FARE_ONLY', false),
    'enable_upper_limit' => env('ENABLE_UPPERLIMIT', true),
  ], //currency
  'gk_tolerance_limits' => [
    'lower_perc' => env('LOWER_GK_LIMIT', '4'),
    'upper_perc' => env('UPPER_GK_LIMIT', '4'),
  ],
  'not_pricing_redirect' => env('NOT_PRICING_REDIRECT', true),
  'forex_country' => 'CA',
  'booking_config' => [
    'partial_booking' => ['tobook' => false, 'booking_settings' => ['yk_entry' => false]],
    'yk_entry_booking_fails' => ['tobook' => false],
    'send_plusup_gds' => 'false', //true or false
    'with_payment' => 'false', //true or false
    'default_moving' => '1',
    'queue_number' => '407',
    'aaa_pricing' => 'false', //true or false
    'save_pricing' => 'false', //true or false
  ],
  'gflights_post_datastore_path' => '/applog/gfsdata/post_data/',
  'gflights_http_datastore_path' => '/applog/gfsdata/http_data/',
  'gflights_cache_timeout' => 1800,
  'gflights_smtp' => [
    'CA' => ['host' => 'smtp.gmail.com', 'port' => '465', 'username' => '<EMAIL>', 'password' => 'csutqzexrgerzovt', 'name' => 'Booking'],
    'US' => ['host' => 'smtp.gmail.com', 'port' => '465', 'username' => '<EMAIL>', 'password' => 'csutqzexrgerzovt', 'name' => 'Booking']
  ],
  'document_mail' => [
    'CA' => [
      'transport'  => 'imap',
      'host'       => 'mail.atozerv.com',
      'port'       => '993',
      'username'   => '<EMAIL>',
      'password'   => 'RAk912TrLDG',
    ],
    'US' => [
      'transport'  => 'imap',
      'host'       => 'mail.atozerv.com',
      'port'       => '993',
      'username'   => '<EMAIL>',
      'password'   => 'AW8kJZkjL#9N',
    ]
  ],
  'feerule_file_prefix' => 'fee',

  'flight_data_source' => env('FLIGHT_DATA_SOURCE', 'sabre'), //sabre=>Sabre,aviation=>Aviation Edge,amadeus=>Amadeus,airlabs=>Airlabs
  'flight_booking_source' => env('FLIGHT_BOOKING_SOURCE', 'sabre'),
  'sabre_book' => 'false',


  'duplicate_booking_msg' => 'We are processing your last booking request for the same itinerary. You will receive your booking confirmation and etickets by email. Please check your spam box as well. Contact our customer care for further support.',
  'duplicate_booking_cache_timeout' => 2500,
  'duplicate_booking_hours_back' => '2', //hours to go back
  'support_roles' => ['agent', 'user', 'manager'],
  'agent_roles' => ['agent', 'user', 'manager'],
  'bookingfile_lockrefresh' =>  10000,
  'booking_index_default_days' => env('BOOKING_INDEX_DEFAULT_DAYS', 500),
  'booking_index_all_tab_days' => env('BOOKING_INDEX_ALL_TAB_DAYS', 500),

  //currency
  'itinerary_baggage_default' => 'NO_BAGGAGE', // 'AS_PER_AIRLINES',
  'popup_message_on_checkout_one' => 'Please note that the price has been changed since the last displayed. Your booking will be processed at the latest updated price as shown.',
  'popup_message_on_checkout_two' => 'No seats are available for the selected fare. We are finding best available fares for your trip...',
  'google_fare_minimum_value' => 1000000,
  'displayedprice_minimun_range' => 0, //currency
  'popup_button_string' => 'Search Now',

  'token_time_out' => 12000,
  'duplicate_booking_hours_back' => '0.5', //hours to go back
  'sys_debug_email' => env("SYS_DEBUG_EMAIL", "<EMAIL>"),

  'ip_address_url' => env('IP_ADDRESS_ENDPOINT', 'https://ipinfo.io/'),
  'ip_address_token' => env('IP_ENDPOINT_TOKEN', 'f3f3fea429cde8'),
  'call_gk_bfm_route' => false,
  'gk_bfm_route' => 'frontend.flights.callbfm', // for bfm change the callgk to callbfm
  'whatsapp_enable' => false,
  'whatsapp_number' => 'https://api.whatsapp.com/send?phone=+16479578400',
  'pricingresponse_data_insertion' => 'http://localhost/airline_store/public/insert-data',
  'flighttimes_data_insertion' => 'http://localhost/airline_store/public/insertflighttimes',
  'airlabs_data_insertion' => 'http://localhost/airline_store/public/insertairlabsdata',
  'retrieve_data' => 'http://localhost/airline_store/public/retrieve',
  // https://aviation-edge.com/v2/public/flights?key=[API_KEY]&limit=30000,
  'payment_confirm_page' => [
    'agree_content' => 'I agree to this itinearary and fare ',
    'link_expiry_days' => 1,
  ],
  'writefileurl' => 'https://booking.traveloplex.com/api/writecustomfile',
  'paymentpage_host_url' => 'https://booking.traveloplex.com',
  'configured_rbd_matching_perc' => '80', //percentage for rbd matching - failed bookings
  'configured_price_difference_perc' => '50', //percentage for price difference-failed bookings
  'price_diff_range' => 120,
  'failed_booking_message' => 'Sorry we weren\'t able to process this booking.Please try again later',
  'price_changed_message_for_payment_link' => "Ticket prices fluctuate throughout the day and, unfortunately, the price has increased since we last checked. Please accepts to continue with the new price or check out alternative trips at Traveloplex.",
  'price_changed_popup_cancel_btn_text' => 'Search with Traveloplex',
  'carrier_booking_blocked_CA' => 'BR,CX,LH,AI,JL,LX,SN,OS,PR,EK,AH,QR,TP,BA,PR,LH,LO,CX,AI,TP,BA,KE,AT,NH,AH,OS,BR,S4,FJ,PK,MS,AU,TU,EK,SN,LX,NZ,BW,QR,TS,SV,AY,SK,RJ,AV,CI,OZ',
  'carrier_booking_blocked_US' => 'AI,BR,CX,JL,LX,SN,OS,PR,EK,AH,QR,TP,BA,PR,LH,LO,CX,AI,TP,BA,KE,AT,NH,AH,OS,BR,S4,FJ,PK,MS,AU,TU,EK,SN,LX,NZ,BW,QR,TS,SV,AY,SK,RJ,AV,CI,OZ',
  'carrier_search_blocked_upperlimit_CA' => ['LH' => '18', 'CM' => '18'], //search blocked positive difference perc- CA
  'carrier_search_blocked_upperlimit_US' => ['LH' => '18', 'CM' => '18'], //search blocked positive difference perc- US
  'carrier_search_blocked_lowerlimit_CA' => ['LH' => '10', 'CM' => '10'], //search blocked negative difference perc- CA
  'carrier_search_blocked_lowerlimit_US' => ['LH' => '10', 'CM' => '10'], //search blocked negative difference perc- US
  'carrier_search_blocked_upperlimit_ALL' => '30', //search blocked positive difference perc- ALL/default
  'carrier_search_blocked_lowerlimit_ALL' => '15', //search blocked negative difference perc- ALL/default
  'highlight_booking_days' => 2,
  'flagged_email_bcc' => env("FLAGGED_EMAIL_BCC", '<EMAIL>'),
  //  'task_queues'=>[
  //   [
  //                'queue_type' => 'ccverify',
  //                'queue_name' => 'CC Verify',
  //                ],
  //                [
  //                'queue_type' => 'ticketing',
  //                'queue_name' => 'Ticketing',
  //                ],
  //                [
  //                'queue_type' => 'request',
  //                'queue_name' => 'Requested',
  //                ],
  //                [
  //                 'queue_type' => 'issued',
  //                 'queue_name' => 'Ticket Issued',
  //                   ],
  //             [
  //                 'queue_type' => 'cancelled',
  //                 'queue_name' => 'Cancelled',
  //                   ],
  //             [
  //                 'queue_type' => 'hold',
  //                 'queue_name' => 'Hold',
  //                  ]
  //                ],
  'task_queues' => [
    [
      'queue_type' => 'ccverify',
      'queue_name' => 'CC Verify',
    ],
    [
      'queue_type' => 'ticketing',
      'queue_name' => 'Ticketing',
    ],
    [
      'queue_type' => 'request',
      'queue_name' => 'Requested',
    ],
    [
      'queue_type' => 'issued',
      'queue_name' => 'Ticket Issued',
    ],
    [
      'queue_type' => 'cancelled',
      'queue_name' => 'Cancelled',
    ],
    [
      'queue_type' => 'hold',
      'queue_name' => 'Hold',
    ]
  ],
  'task_delete_queues' => [
    [
      'queue_type' => 'booking_delete',
      'queue_name' => 'Delete',
    ]
  ],

  //  'supplier_list'=>[
  //   [
  //                'supplier_name' => 'supplier1',

  //                ],
  //                [
  //                'supplier_name' => 'supplier2',

  //                ],
  //                [
  //                'supplier_name' => 'supplier3',

  //                  ]
  //                ],
  'supplier_list' => env("SUPPLIER_LIST", "TRAVELOPLEX-CA"),

  'agency_timezone' => 'America/Toronto',
  'agentportal_domain' => 'b2bportal.traveloplex.com',
  'agentportal_source' => 'agentportal',
  'whatsapp_support_travelopl' => [
    // 'api_key' => env('API_KEY', 'default_api_key'),
    'api_key' => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY1OGFkNWNmZjEyYzZmMGI0NDZhYjM4NiIsIm5hbWUiOiJCTVQgV2hhdHNhcHAiLCJhcHBOYW1lIjoiQWlTZW5zeSIsImNsaWVudElkIjoiNjU4NmQE3MDM1OTc1MTl9.chxjXqXxEAhZjkcjB_lHOH1rlo3YmG6tpbfjcEEUXl4',
    'source' => 'TREVELOPLES',
    'whatsapp_hit_link' => 'https://backend.aisensy.com/campaign/t1/api/v2',
    'support_email' => 'traveloplex.com'
  ],
  'whatsapp_support_traveloplex' => [
    // 'api_key' => env('API_KEY', 'default_api_key'),
    'api_key' => 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY1OGFkNWNmZjEyYzZmMGI0NDZhYjM4NiIsIm5hbWUiOiJCTVQgV2hhdHNhcHAiLCJhcHBOYW1lIjoiQWlTZW5zeSIsImNsaWVudElkIjoiNjU4NmQ0OTk3NGNmM2MwYjQzMGQwODdkIiwiYWN0aXZlUGxhbiI6Ik5PTkUiLCJpYXQiOjE3MDM1OTc1MTl9.chxjXqXxEAhZjkcjB_lHOH1rlo3YmG6tpbfjcEEUXl4',
    'source' => 'TRAVELOPLEX',
    'whatsapp_hit_link' => 'https://backend.aisensy.com/campaign/t1/api/v2',
    'support_email' => 'traveloplex.com'
  ],
  'whatsapp_support_campaign_name_traveloplex_us' => [
    'start' => 'booking_support_3',
    'continue' => 'bmt_continue_conversation',
    'updatePayment' => 'update_payment_1',
  ],
  'whatsapp_support_campaign_name_traveloplex_ca' => [
    'start' => 'booking_support_3',
    'continue' => 'bmt_continue_conversation',
    'updatePayment' => 'update_payment_1',
  ],

  'gfs_index_default_days' => '2',
  'template_email_bcc' => env('TEMPLATE_EMAIL_BCC', '<EMAIL>'),

  'mail_address_from' => [
    'CA' => env('MAIL_FROM_ADDRESS_CA', '<EMAIL>'),
    'US' => env('MAIL_FROM_ADDRESS_US', '<EMAIL>')
  ],
  'mail_address_from_source' => [
    'B2B' => env('MAIL_FROM_ADDRESS_B2B', '<EMAIL>'),
    'B2C' => env('MAIL_FROM_ADDRESS_B2C', '<EMAIL>')
  ],

  'content_sources_received_from' => 'Citiairtravel',

  'content_sources_product' => [
    'flights' => 'Flights',
    'hotel' => 'Hotel'
  ],

  'currency_pos' => [
    'CAD' => 'CA',
    'USD' => 'US',
    'GBP' => 'GB'
  ],

  'email_portal_email_source' => [
    'B2B' => env('EMAIL_PORTAL_EMAIL_B2B', '<EMAIL>'),
    'B2C' => env('EMAIL_PORTAL_EMAIL_B2C', '<EMAIL>')
  ],
  'email_portal_phone' => [
    'CA' => env('EMAIL_PORTAL_PHONE_CA', ''),
    'US' => env('EMAIL_PORTAL_PHONE_US', '')
  ],
  'email_portal_phone_source' => [
    'B2B' => env('EMAIL_PORTAL_PHONE_B2B', ''),
    'B2C' => env('EMAIL_PORTAL_PHONE_B2C', '')
  ],
  'email_subject_mapping' => [
    "Booking in Process: Confirm Now!" => 'Booking in Process: Confirm Now!',
    "Booking Confirmation Proposal" => 'Booking Confirmation Proposal',
    "E-Ticket Confirmation" => 'E-Ticket Confirmation',
    "E-Ticket2 Confirmation" => 'E-Ticket Confirmation',
    "Update Payment" => 'Urgent Attention Required',
    "Update Payment With Itinerary" => 'Urgent Attention Required',
    "Unable to Confirm Your Reservation!" => 'Unable to Confirm Your Reservation!',
    "Credit Card Declined!" => 'Credit Card Declined!',
    "Booking Cancelled: Pending Payment!" => 'Booking Cancelled: Pending Payment!',
    "Card Verification Pending" => 'Card Verification Pending',
    "Credit Card Authorization Required" => 'Credit Card Authorization Required',
    "Card Verification Completed" => 'Card Verification Completed',
    "Exchange Request" => 'Exchange Request',
    "Your Refund Request" => 'Your Refund Request',
    "Refund Confirmation" => 'Refund Confirmation',
    "Refund Rejected" => 'Refund Rejected',
    "Schedule Change Notification" => 'Schedule Change Notification',
    "Your Revised Flight Confirmation" => 'Your Revised Flight Confirmation',
    "Chargeback Information" => 'Chargeback Information',
    "Cancellation Confirmation" => 'Cancellation Confirmation',
    "Unfulfilled Ancillary Request" => 'Unfulfilled Ancillary Request',
  ],
  'email_support_id_prefix' => 'mt',
  'min_percentage_string' => '75',
  'adult_icon_path' => 'https://cdn.vibrace.com/resources/clients/travel_assets/email/adult_icon.png',
  'child_icon_path' => 'https://cdn.vibrace.com/resources/clients/travel_assets/email/childBoy_icon.png',
  'infant_icon_path' => 'https://cdn.vibrace.com/resources/clients/travel_assets/email/infant_icon.png',
  'plane_icon_path' => 'https://cdn.vibrace.com/resources/clients/travel_assets/email/plane_icon.png',
  'clock_icon_path' => 'https://cdn.vibrace.com/resources/clients/travel_assets/email/clock_icon.png',

  //choose between two different routes based on the API type
  "mail_api_type" => "v1",
  //    "v1"= "Pubbly email api",
  //    "v2"= "Microsoft email api",
  "payment_gateway_view_url" => "https://traveloplex.com/paymentgateway/show?id=",
  'paymentpage_host_url' => 'https://traveloplex.com/',
  'update_reason' => [
    'cancelled' => [
      'Credit card declined',
      'Requested by passenger',
      'Duplicate booking',
      'Test Booking'
    ],
    'hold' => [
      'Card verification awaited.',
      'Credit card declined. Payment awaited.',
      'Requested by passenger',
    ],
  ],
  "dashboard_logo" => env('DASHBOARD_LOGO_PATH'),

  'booking_source' => ['gfs', 'portal'],
  "eticket_add_ons_visible" => true,
  "pos_filter" => ['us'],
  'local_timezone' => [
    'EDT' => 'EST',
    'PDT' => 'PST',
    'IST' => 'IST'
  ],
  'pos_timezone' => [
    'CA' => 'America/Toronto',
    'US' => 'Asia/Kolkata',
  ],
  'pos_credit_limit' => [
    'CA' => 'CAD',
    'US' => 'USD',
    'GB' => 'GBD'
  ],
  'pnr_retrieve' => [
    'pnr' => 'pnr',
    'itinerary' => 'itinerary'
  ],
  'Title' => [
    'Mr' => 'Mr',
    'Mrs' => 'Mrs',
    'Miss' => 'Miss',
    'Ms' => 'Ms',
    'Other' => 'Other',
  ],
  'Designation' => [
    'Owner' => 'Owner',
    'Manager' => 'Manager',
    'Supervisor' => 'Supervisor',
    'Consultant' => 'Consultant',
    'Others' => 'Others',
  ],
  'country_code' => [
    'United States(+1)' => '+1',
  ],
  'Regiter_country' => [
    'United States' => 'United States',
  ],
  'Regiter_state' => [
    'Alberta' => 'Alberta',
    'British Columbia' => 'British Columbia',
    'Manitoba' => 'Manitoba',
    'New Brunswick' => 'New Brunswick',
    'Newfoundland' => 'Newfoundland',
    'Northwest Territories' => 'Northwest Territories',
    'Nova Scotia' => 'Nova Scotia',
    'Nunavut' => 'Nunavut',
    'Ontario' => 'Ontario',
    'Prince Edward Island' => 'Prince Edward Island',
    'Quebec' => 'Quebec',
    'Saskatchewan' => 'Saskatchewan',
    'Yukon Territory' => 'Yukon Territory'
  ],
  'pnr_retrieve_sources' => [
    [
      'gds' => 'Sabre',
      'gds_Id' => '1S',
      'pcc' => 'G6CF',
    ]
  ],

  'content_sources_gds' => [
    '1S' => 'Sabre',
    '1A' => 'Amadeus'
  ],
  // 'agent_group_value' =>[
  //   'Default Group' => 'default group',
  // ],
  'super_admin_email' => env('SUPER_ADMIN_EMAIL', ''),
  'admin_email' => env('ADMIN_EMAIL', ''),

  'b2b_portal_url' => 'https://b2bportal.traveloplex.com',
  'pnr_retrieval_api_url' => 'https://booking.traveloplex.com/api/pnrretrieval',
  'pnr_retrieval_addbooking_api_url' => 'https://booking.traveloplex.com/api/pnrretrievaladd',
  // 'tab_favicon' => 'https://cdn.vibrace.com/resources/clients/travel/favicon_wbg.png',
  'tab_favicon' => 'http://127.0.0.1:8000/assets/img/logo/favicon.png',
  'check_agent_allowed_domain' => 'agents.traveloplex.com',
  'employee_agency_group_id' => 'HHbU1S9wDM',
  'login_page_img' => 'assets/img/pages/crmtraveloplex.jpg',
  'agency_name_invoice' => "Traveloplex",
  'airline_status' => [
    'Claimed' => 'Claimed',
    'Received' => 'Received',
    'Hold' => 'Hold',
    'Disport'  => 'Disport',
  ],
  'company_name' => env('COMPANY_NAME', ''),
  // 'sabre_cancel_url' =>'http://127.0.0.1:8000/api/flightCancelRequest',
  'sabre_cancel_url' => 'https://booking.traveloplex.com/api/sabrecancel',
  'disable_cancellation' => [
    'gds_booked' => true,
    'system_booked' => false
  ],
  'mailer_encryption' => [
    'tls' => 'tls',
    'ssl' => 'ssl',
  ],
  'pcc_id' => '1S',
  'insert_pnr_api_url' => 'https://booking.traveloplex.com/api/pnrretrievaladd',


  'agent_booking_view_list' => [
    'send_email',
    'eticket',
    'notes',
  ],
  'employee_booking_view_list' => [
    'send_email',
    'eticket',
    'audit_trail',
    'security_data',
    'eticket_template',
    'notes',
    'commission_plan',
    'agent_commission_calculation',
    'gds_fare_info',
    'markup',
  ],
  'eticke_no_Retrieve_url' => 'https://booking.traveloplex.com/api/pullbookingsatus',
  'booking_status' => [
    'Booked' => 'booked',
    'Failed' => 'failed',
    'Cancelled' => 'cancelled',
    'Hold' => 'hold',
    'Ticketed' => 'ticketed',
  ],
  'payment_status' => [
    'Earned' => 'earned',
    'Paid' => 'paid',
    'Cancelled' => 'cancelled',
  ],
  'booking_status_update_api_endpoint' => 'https://otmapi.traveloplex.com/api/bookingstatusmodify',
  'agent_default_group_id' => 'smptravel',
  "logo" => env('SITE_LOGO_PATH'),
  'default_group_name' => 'vtravel INTERNATIONAL',
  'user_phone_system' => [
    'net2phone' => 'Net2Phone',
    'phoneAny' => 'PhoneAny'
  ],
  'b2c_portal_language' => [
    'Canada' => 'ca',
    'US' => 'en',
    'Spanish' => 'es',
    'French' => 'fr',
    'German' => 'de',
    'Italian' => 'it',
    'Portuguese' => 'pt',
    'Chinese' => 'zh',
    'Portuguese' => 'pt',
    'Chinese' => 'zh',
  ],
  'portal_type' => [
    'B2B' => 'b2b',
    'B2C' => 'b2c',
  ],
  'portal_domain_B2B' => 'b2bportal.travel.com',
  'booked_source' => [
    'GDSBOOKED' => 'M',
    'SYSTEMBOOKED' => 'E'
  ],
  'content_manage' => [

    'api_endpoint' => 'https://booking.traveloplex.com/api',

    'service' => 'contentmanage',
    'content_list' => [
      [
        'content_id' => 'aboutus',
        'content_name' => 'About Us',
      ],
      [
        'content_id' => 'privacypolicy',
        'content_name' => 'Privacy Policy',
      ],
      [
        'content_id' => 'termsofuse',
        'content_name' => 'Terms Of Use',
      ],
      [
        'content_id' => 'bookingconfirmterms',
        'content_name' => 'Booking Confirm Terms',
      ],
      [
        'content_id' => 'footernotes',
        'content_name' => 'Footer Notes',
      ],
      [
        'content_id' => 'contactus',
        'content_name' => 'Contact Us',
      ],
    ],
    'domains' => [
      [
        'domain' => 'traveloplex.com',
        'display_name' => 'traveloplexb2cus',
        'languages' => 'en,es',
        'contents' => 'aboutus,privacypolicy,termsofuse,bookingconfirmterms,footernotes,contactus',
      ],
      [
        'domain' => 'traveloplex.ca',
        'display_name' => 'traveloplexb2cca',
        'languages' => 'en',
        'contents' => 'aboutus,privacypolicy,termsofuse,bookingconfirmterms',
      ],
      [
        'domain' => 'b2bportal.traveloplex.com',
        'display_name' => 'traveloplexb2b',
        'languages' => 'en',
        'contents' => 'aboutus,privacypolicy,termsofuse,bookingconfirmterms',
      ]
    ],
  ],
  'oauth_token_url' => 'https://integrate.versature.com/api/oauth/token/',
  'client_secret' => '0nlOua4ZOyjE4SYirGsLNE3U7K8IdYZYCKGPfhOeB76xv9KTcQ',
  'client_id' => '****************',
  'allow_to_call' => 'https://integrate.versature.com/api/calls/',
  'Cabin_class' => [
    'economy' => 'ECONOMY',
    'premium' => 'PREMIUM',
    'business' => 'BUSINESS',
    'first' => 'FIRST',
    'any' => 'ANY'
  ],
  'E_ticket_portal_logo' => "http://127.0.0.1:8000/assets/img/logo/favicon.png",
  "E_TICKET_NOTES" => "
      Trip cancellation and medical insurance offered.... Passengers are responsible to obtain
      required visas and documents prior to
      commencement of travel please reconfirm all flights 72 hours before departure please ensure all
      names dates flights and travel
      documents are correct when you receive them please call us immediately if there are any
      discrepencies with your documents rcvd at
      ********** v travel cannot be held responsible for charges initiated by airline
      touroperator and consolidator or other
      independent parties who have been contracted for services you may have purchased all deposits
      are non refundable fares and seats
      are not guaranteed if the balance amount is not received on due date any fees service charges
      are completely non-refundable and
      surcharges taxes are subject to change as per airline/touroperator consolidator prices not
      guaranteed if payment is not rcvd in full.
      Entry to another country may be refused even if the required information and travel documents
      are complete living standards
      accommodations services practices and other conditions at destination may differ from those
      found in canada this contract permits
      price increases no price increases are permitted after the customeer has paid in full if the
      total price of the travel services is increased
      and the cumulative increase except any increase resulting in retail sales tax or federal goods
      and services tax is more than 7per the
      customer has the right to cancel the contract and obtain a full rfnd when you book with v
      travel you agree with the terms and
      conditions stated on this page **********have a safe and pleasant trip**************** ticket is
      non-refundable change penalty cad 200.00
      Each way fare and tax difference may apply for change total fare cad 875.00 All deposits are
      non-refundable paid cad 300.00 Cash
      deposit 11feb25 balance due cad 575.0
    ",

  'Transaction_mode' => [
    'credit' => 'Credit',
    'debit' => 'Debit'
  ],
  'Transaction_Type' => [
    'deposit' => 'Deposit',
    'payment' => 'Payment'
  ],
  'Debit_Type' => [
    'account_cash' => 'Account Cash',
    'refund' => 'Refund'
  ],
  'Credit_rule' => [
    'block_search' => 'Block Search',
    'block_booking' => 'Block Booking',
    'block_ticketing' => 'Block Ticketing',
    'no_booking' => 'No Booking'
  ],
  'default_cancelation_charge' => 300,

  'default_credit_rule' => 'block_search',
  'currency_pos' => [
    'CAD' => 'CA',
    'USD' => 'US'
  ],
  'net2phoneEnabled' => false,
  'Market_type' => [
    'b2b' => 'B2B',
    'b2c' => 'B2C',
    'meta' => 'Meta'
  ],
  'currency_block' => [
    'portal' => [
      [
        'portal_id' => 'https://traveloplex.ca',
        'portal_name' => 'traveloplex.ca',
      ],
      [
        'portal_id' => 'https://traveloplex.com',
        'portal_name' => 'traveloplex.com',
      ],
      [
        'portal_id' => 'https://b2bportal.traveloplex.com',
        'portal_name' => 'b2bportal.traveloplex.com',
      ]

    ],
    'market_type' => [
      [
        'market_value' => 'b2b',
        'display_name' => 'B2B',
        'portal' => 'https://b2bportal.traveloplex.com',
      ],
      [
        'market_value' => 'b2c',
        'display_name' => 'B2C',
        'portal' => 'https://traveloplex.com,https://traveloplex.ca',
      ],
      [
        'market_value' => 'meta',
        'display_name' => 'Meta',
        'portal' => 'https://traveloplex.com,https://traveloplex.ca',
      ],

    ],
  ],
  'fare_source' => ['meta', 'portal'],
  'fare_types' => [
    'published' => 'PUBLISHED',
    'private' => 'PRIVATE',
    'net' => 'NET'
  ],
  'fare_pos' => [
    "ca" => "CA",
    "com" => 'US'
  ],
  'pos' => [
    'US' => 'US',
    'CA' => 'CA',
  ],
  'gds_type' => [
    'amadeus' => '1a',
    'sabre' => '1s',
    'clarity' => '1c'
  ],
  'content_sources' => [
    0 => ["id" => 's1xmsttxg6', "type" => 'sabre', "display" => 'SABRE'],
    1 => ["id" => 'c3jssttxsk', "type" => 'clarity', "display" => 'CLARITY'],
  ],
  'feerule_file_folder_path' => 'appcache_files/feerules/',

  'content_sources_airline_masking'=>[
    "scity1" =>[
    'content_type'=>'sabre',
        'username' => '839524',
        'password' => '',
        'pcc' => '18ZK',
        'gdstype' => '1S',
        'domain' => 'DEFAULT',
        'dk_number' => 'N908248424',
        'gdstype' => '1S',
        'end_trans_given_name' => 'CITIAIRTRAVEL',
        'aaa_pcc' => '18ZK',
        'endpoint' => 'https: //webservices.platform.sabre.com',
        'agency_address' => [
    'line1'   => '1367 Oak Tree Rd',
    'line2'   => '',
    'city'    => 'Iselin',
    'state'   => 'NJ',
    'pincode' => '08830',
    'country' => 'USA'
        ],
    ],
    "scity2" => [
      'content_type'=>'sabre',
        'username' => '219409',
        'password' => '',
        'pcc' => '8WLF',
        'gdstype' => '1S',
        'domain' => 'DEFAULT',
        'dk_number' => 'N888248469',
        'gdstype' => '1S',
        'end_trans_given_name' => 'CITIAIRTRAVEL',
        'aaa_pcc' => '8WLF',
        'endpoint' => 'https: //webservices.platform.sabre.com',
        'agency_address' => [
    'line1'   => '1367 Oak Tree Rd',
    'line2'   => '',
    'city'    => 'Iselin',
    'state'   => 'NJ',
    'pincode' => '08830',
    'country' => 'USA'
        ],
    ],
    "scity3" =>[
      'content_type'=>'sabre',
        'username' => '805314',
        'password' => '',
        'pcc' => 'W4FF',
        'gdstype' => '1S',
        'domain' => 'DEFAULT',
        'dk_number' => 'N888248469',
        'gdstype' => '1S',
        'end_trans_given_name' => 'CITIAIRTRAVEL',
        'aaa_pcc' => 'W4FF',
        'endpoint' => 'https: //webservices.platform.sabre.com',
        'agency_address' => [
    'line1'   => '1367 Oak Tree Rd',
    'line2'   => '',
    'city'    => 'Iselin',
    'state'   => 'NJ',
    'pincode' => '08830',
    'country' => 'USA'
        ],
    ],
    "scity4" => [
      'content_type'=>'sabre',
        'username' => '136391',
        'password' => '',
        'pcc' => 'D7LI',
        'gdstype' => '1S',
        'domain' => 'AA',
        'dk_number' => '8882484697',
        'gdstype' => '1S',
        'end_trans_given_name' => 'CITIAIRTRAVEL',
        'aaa_pcc' => '9N60',
        'endpoint' => 'https: //webservices.platform.sabre.com',
        'agency_address' => [
    'line1'   => '1367 Oak Tree Rd',
    'line2'   => '',
    'city'    => 'Iselin',
    'state'   => 'NJ',
    'pincode' => '08830',
    'country' => 'USA'
        ],
    ]
  ],

  'citiaircache' => 'citiairtravel',

  'newsLetterRecordsPerPage' => 10,
  'news_letter_categories' => [
    'news' => 'News',
    'promotions' => 'Promotions',
    'uncategorized' => 'Uncategorized',
    'airline updates' => 'Airline Updates'
  ],
  'fare_domain' => [
    'traveloplex.ca' => [
      'domain' => 'traveloplex.ca',
      'market_country' => 'CA'
    ],
    'traveloplex.us' => [
      'domain' => 'traveloplex.us',
      'market_country' => 'US'
    ],
    'flexpay.com' => [
      'domain' => 'flexpay.com',
      'market_country' => 'CA'
    ]
  ],
  "blog_folder" => "blog_attachments",
  "b2b_portal_pos" => "CA",
  "masking_log_path" => "uploads/logos"
]
?>
