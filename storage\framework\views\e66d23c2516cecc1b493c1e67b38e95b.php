<?php $__env->startSection('title', 'Airline Masking'); ?>

<?php $__env->startSection('vendor-style'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/5.1.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/tagify/tagify.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/typeahead-js/typeahead.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')); ?>"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"
        integrity="sha256-xLD7nhI62fcsEZK2/v8LsBcb4lG7dgULkuXoXB/j91c=" crossorigin="anonymous"></script>

    <!-- Flat Picker -->
    <script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/tagify/tagify.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/forms-typeahead.js')); ?>"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<?php $__env->stopSection(); ?>

<style>
    .flatpickr-wrapper {
        display: block !important;
        position: relative;
    }
</style>
<?php $__env->startSection('content'); ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>


    <div class="card">
        <div class="card-body" style="padding:20px 35px;">
            <div class="row mb-4">
                <div class="col-11">
                    <h5>Create Airline Masking</h5>
                </div>
                
            </div>

            <form method="POST" action="<?php echo e(route('backend.airline_masking.store')); ?>" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                
                
                <div id="parent-container">
                    <div class="card row p-3" id="repeatable-section-0">
                        <div class="row">
                            <div class="col-lg-2">
                                <label>Portal Mode</label>
                                <select name="portal_mode-0" id="portal_mode-0" class="form-control required select2"
                                    required>
                                    <option value="PORTAL">Portal</option>
                                    <option value="META">Meta</option>
                                </select>
                            </div>
                            <div class="col-lg-2">
                                <label>POS</label>
                                <select name="pos-0" id="pos-0" class="form-control required select2" required
                                    placeholder="POS">
                                    <option value="US">US</option>
                                    <option value="CA">CA</option>
                                </select>
                            </div>

                            <div class="col-lg-2">
                                <label>Portal</label>
                                <div class="select2-primary">
                                    <select id="portal-0" class="form-select select2" required name="portal-0">
                                        <?php $__currentLoopData = config('custom-app.portals'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $urls): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php $__currentLoopData = $urls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e(str_replace('https://', '', $url)); ?>">
                                                    <?php echo e(str_replace('https://', '', $url)); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-2">
                            <label>Content Sources</label>
                                <select id="content_sources-0" class="form-select select2" name="content_sources-0[]" multiple>
                                    <?php $__currentLoopData = $formatted_content_sources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>"><?php echo e($value); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                            <div class="col-lg-2">
                                <label>Fare Types</label>
                                <div class="select2-primary">
                                    <select id="fareType-0" class="form-select select2" required name="fare_type-0[]"
                                        multiple>
                                        <option value="Published" selected>Published</option>
                                        <option value="Private" selected>Private</option>
                                        <option value="Net" selected>Net</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-lg-2 text-end">
                                <button type="button" id="add-item" class="btn btn-sm fs-5 btn-primary"
                                    onclick="addItem()">+ </button>
                            </div>

                        </div>


                        
                        <div class="row">
                            <div class="col-lg-4 mt-3">
                                <label>Origin</label>
                                <fieldset class="p-3 border rounded origin-group" label="Origin">
                                    <div class="d-flex mb-2" style="text-wrap: balance;">

                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="origin-group-radio-0" name="originRadio-0"
                                                value="1" class="originRadio form-check-input"
                                                onchange="handleOriginRadio(0,1)" checked>
                                            <label class="mb-0 mx-1 form-label">Group</label>
                                        </div>
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="origin-country-radio-0" name="originRadio-0"
                                                value="2" class="originRadio form-check-input"
                                                onchange="handleOriginRadio(0,2)">
                                            <label class="mb-0 mx-1 form-label">Country</label>
                                        </div>
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="origin-airport-radio-0" name="originRadio-0"
                                                value="3" class="originRadio form-check-input"
                                                onchange="handleOriginRadio(0,3)">
                                            <label class="mb-0 mx-1 form-label">Airport</label>
                                        </div>
                                    </div>


                                    <div class="form-group origin-group-container" id="origin-group-container-0">
                                        <select rows="1" name="origin_group-0"
                                            class="form-control textBox-style w-100" placeholder="Enter location group"
                                            id="origin-group-0">
                                            <?php if(!empty($location_group)): ?>
                                                <?php $__currentLoopData = $location_group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($item); ?>"><?php echo e($item); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>

                                    <div class="form-group origin-country-container" id="origin-country-container-0"
                                        style="display:none;">
                                        <textarea rows="1" name="origin_country-0" class="form-control textBox-style w-100"
                                            placeholder="Enter country" id="origin-country-0"></textarea>
                                    </div>

                                    <div class="form-group origin-airport-container" id="origin-airport-container-0"
                                        style="display:none;">
                                        <textarea rows="1" name="origin_airport-0" class="form-control textBox-style w-100"
                                            placeholder="Enter airports" id="origin-airport-0"></textarea>
                                    </div>
                                </fieldset>
                            </div>


                            
                            <div class="col-lg-4 mt-3">
                                <label>Destination</label>
                                <fieldset class="p-3 border rounded destination-group">
                                    <div class="d-flex mb-2" style="text-wrap: balance;">
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="destination-group-radio-0"
                                                name="destinationRadio-0" value="4"
                                                class="destinationRadio form-check-input"
                                                onchange="handleDestinationRadio(0,4)" checked>
                                            <label class="mb-0 mx-1 form-label">Group</label>
                                        </div>
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="destination-country-radio-0"
                                                name="destinationRadio-0" value="5"
                                                class="destinationRadio form-check-input"
                                                onchange="handleDestinationRadio(0,5)">
                                            <label class="mb-0 mx-1 form-label">Country</label>
                                        </div>
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="destination-airport-radio-0"
                                                name="destinationRadio-0" value="6"
                                                class="destinationRadio form-check-input"
                                                onchange="handleDestinationRadio(0,6)">
                                            <label class="mb-0 mx-1 form-label">Airport</label>
                                        </div>
                                    </div>


                                    <div class="form-group destination-group-container"
                                        id="destination-group-container-0">
                                        <select rows="1" name="destination_group-0"
                                            class="form-control textBox-style w-100" placeholder="Enter location group"
                                            id="destination-group-0">
                                            <?php if(!empty($location_group)): ?>
                                                <?php $__currentLoopData = $location_group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($item); ?>"><?php echo e($item); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>

                                    <div class="form-group destination-country-container"
                                        id="destination-country-container-0" style="display:none;">
                                        <textarea rows="1" name="destination_country-0" class="form-control textBox-style w-100"
                                            placeholder="Enter country" id="destination-country-0"></textarea>
                                    </div>

                                    <div class="form-group destination-airport-container"
                                        id="destination-airport-container-0" style="display:none;">
                                        <textarea rows="1" name="destination_airport-0" class="form-control textBox-style w-100"
                                            placeholder="Enter airports" id="destination-airport-0"></textarea>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-2 mt-5">
                                <input type="checkbox" class="form-check-input" checked name="call_only_fare-0"
                                    id="call_only_fare-0">
                                Call Only Fares
                                <br>
                                <br>
                                <input type="checkbox" class="form-check-input" name="booking-0" id="booking-0">
                                Booking
                            </div>
                            <div class="col-md-2 mt-5" id="booking_types-0">
                                <label>Booking Mode</label>
                                <select name="booking_type-0" id="booking_type-0" class="form-control required select2"
                                    required placeholder="booking type">
                                    <option value="gds">GDS</option>
                                    <option value="system">SYSTEM</option>
                                </select>
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" checked name="show_carrier-0"
                                    id="show_carrier-0">
                                Show Carrier
                                <br>
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" checked name="mask_validating_carrier-0"
                                    id="mask_validating_carrier-0">
                                Mask Validating Carrier
                                <br>
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" checked name="show_flight_timing-0"
                                    id="show_flight_timing-0">
                                Show Flight Timing
                                <br>
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" checked name="mask_marketing_carrier-0"
                                    id="mask_marketing_carrier-0">
                                Mask Marketing Carrier
                                <br>
                            </div>
                            
                        </div>
                        <div class="row mt-4">
                            <div class="col-lg-2">
                                <input type="checkbox" class="form-check-input" checked name="status-0" id="status-0">
                                Active
                            </div>
                        </div>
                    </div>
                    
                </div>

                
                <div class="row mt-4">
                    <div class="col-lg-2">
                        <button class=" btn btn-secondary"><a
                                href="<?php echo e(route('backend.airline_masking.index')); ?>">Back</a></button>
                    </div>
                    <div class="col-lg-9"></div>
                    <div class="col-lg-1">
                        <button class=" btn btn-primary" type="submit">Create</button>
                    </div>
                </div>
                <input type="text" id="countinput" name="count" value="0" style="display:none;">

            </form>
        </div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const callOnlyFareCheckbox = document.getElementById("call_only_fare-0");
            const bookingCheckbox = document.getElementById("booking-0");
            const bookingTypeContainer = document.getElementById(
                "booking_types-0"); // wrap the select in a container div

            function toggleBookingTypeVisibility() {
                if (bookingCheckbox.checked) {
                    bookingTypeContainer.style.display = "block";
                } else {
                    bookingTypeContainer.style.display = "none";
                }
            }

            callOnlyFareCheckbox.addEventListener("change", function() {
                if (this.checked) {
                    bookingCheckbox.checked = false;
                    toggleBookingTypeVisibility();
                }
            });

            bookingCheckbox.addEventListener("change", function() {
                if (this.checked) {
                    callOnlyFareCheckbox.checked = false;
                }
                toggleBookingTypeVisibility();
            });
            toggleBookingTypeVisibility();
        });
    </script>


    <script>
        $(document).ready(function() {
            $('.select5').select2();
            $('#fareType-0').select2();
            $('#pos-0').select2();
            $('#portal-0').select2();
            $('#portal_mode-0').select2();
            $('#booking_type-0').select2();
            $('#content_sources-0').select2();
        });

        function validationCheck(count) {
            let validating = document.querySelector(`#validating-${count}`);
            let marketing = document.querySelector(`#marketing-${count}`);

            if (!marketing.checked && !validating.checked) {
                validating.checked = true;
            }
        }

        function loadCountryList(count) {
            var textarea1 = document.querySelector(`#origin-country-${count}`);
            var textarea2 = document.querySelector(`#destination-country-${count}`);
            var origin_country_tagify = new Tagify(textarea1, {
                enforceWhitelist: true,
                whitelist: [],
                dropdown: {
                    maxItems: 20,
                    classname: "custom-look",
                    enabled: 1,
                    closeOnSelect: true
                }
            });
            var destination_country_tagify = new Tagify(textarea2, {
                enforceWhitelist: true,
                whitelist: [],
                dropdown: {
                    maxItems: 20,
                    classname: "custom-look",
                    enabled: 1,
                    closeOnSelect: true
                }
            });

            let countriesReceived = <?php echo json_encode($country); ?>;
            let country_list = [];

            countriesReceived.forEach(function(item) {
                country_list.push(item);
            });
            origin_country_tagify.settings.whitelist = country_list;
            destination_country_tagify.settings.whitelist = country_list;
        };

        function loadAirportList(count) {
            console.log(count, 'countList');
            var textarea1 = document.querySelector(`#origin-airport-${count}`);
            var textarea2 = document.querySelector(`#destination-airport-${count}`);
            var textarea3 = document.querySelector(`#airlines-airport-${count}`);

            var origin_airport_tagify = new Tagify(textarea1, {
                enforceWhitelist: true,
                whitelist: [],
                dropdown: {
                    maxItems: 20,
                    classname: "custom-look",
                    enabled: 1,
                    closeOnSelect: true
                }
            });
            var destination_airport_tagify = new Tagify(textarea2, {
                enforceWhitelist: true,
                whitelist: [],
                dropdown: {
                    maxItems: 20,
                    classname: "custom-look",
                    enabled: 1,
                    closeOnSelect: true
                }
            });
            var airlines_airport_tagify = new Tagify(textarea3, {
                enforceWhitelist: true,
                whitelist: [],
                dropdown: {
                    maxItems: 20,
                    classname: "custom-look",
                    enabled: 1,
                    closeOnSelect: true
                }
            });

            let airportsReceived = <?php echo json_encode($airport); ?>;
            let airlinesReceived = <?php echo json_encode($airline); ?>;
            let airport_list = [];
            let airline_list = [];

            airportsReceived.forEach(function(item) {
                airport_list.push(`${item['name']}`);
            });
            airlinesReceived.forEach(function(item) {
                airline_list.push(item.code + '-' + item.name);
            });
            origin_airport_tagify.settings.whitelist = airport_list;
            destination_airport_tagify.settings.whitelist = airport_list;
            airlines_airport_tagify.settings.whitelist = airline_list;
        }


        function handleOriginRadio(count, selectedValue) {
            $(`#origin-group-container-${count}, #origin-country-container-${count}, #origin-airport-container-${count}`)
                .hide();
            if (selectedValue == 1) {
                $(`#origin-group-container-${count}`).show();

                $(`#origin-airport-container-${count}`).removeAttribute('required');
                $(`#origin-airport-container-${count}`).hide();

                $(`#origin-country-container-${count}`).removeAttribute('required');
                $(`#origin-country-container-${count}`).hide();
            } else if (selectedValue == 2) {
                loadCountryList(count);
                $(`#origin-country-container-${count}`).show();

                $(`#origin-group-container-${count}`).removeAttribute('required');
                $(`#origin-group-container-${count}`).hide();

                $(`#origin-airport-container-${count}`).removeAttribute('required');
                $(`#origin-airport-container-${count}`).hide();
            } else if (selectedValue == 3) {
                loadAirportList(count);
                $(`#origin-airport-container-${count}`).show();

                $(`#origin-group-container-${count}`).removeAttribute('required');
                $(`#origin-group-container-${count}`).hide();

                $(`#origin-country-container-${count}`).removeAttribute('required');
                $(`#origin-country-container-${count}`).hide();
            }
        }

        function handleDestinationRadio(count, selectedValue) {
            $(`#destination-group-container-${count}, #destination-country-container-${count}, #destination-airport-container-${count}`)
                .hide();

            if (selectedValue == 4) {
                $(`#destination-group-container-${count}`).show();
                $(`#destination-airport-container-${count}`).removeAttribute('required');
                $(`#destination-airport-container-${count}`).hide();
                $(`#destination-country-container-${count}`).removeAttribute('required');
                $(`#destination-country-container-${count}`).hide();
            } else if (selectedValue == 5) {
                loadCountryList(count);
                $(`#destination-country-container-${count}`).show();
                $(`#destination-group-container-${count}`).removeAttribute('required');
                $(`#destination-group-container-${count}`).hide();
                $(`#destination-airport-container-${count}`).removeAttribute('required');
                $(`#destination-airport-container-${count}`).hide();
            } else if (selectedValue == 6) {
                loadAirportList(count);
                $(`#destination-airport-container-${count}`).show();
                $(`#destination-group-container-${count}`).removeAttribute('required');
                $(`#destination-group-container-${count}`).hide();
                $(`#destination-country-container-${count}`).removeAttribute('required');
                $(`#destination-country-container-${count}`).hide();
            }
        }

        function handleAirlinesRadio(count, selectedValue) {
            console.log('handleAirlinesRadio', selectedValue);
            if (selectedValue === 8) {
                $(`#airlines-airport-container-${count}`).show();
                loadAirportList(count);
            }
        }
        $(document).ready(function() {
            const count = 0;
            const selectedValue = $(`input[name="airlinesRadio-${count}"]:checked`).val();
            console.log(count, selectedValue);

            handleAirlinesRadio(count, parseInt(selectedValue));
        });

        let count = 0;

        function addItem() {
            const containers = document.querySelectorAll('[id^="repeatable-section-"]');
            containers.forEach((item) => {
                const id = item.id;
                const suffix = parseInt(id.split('-').pop(), 10);
                if (!isNaN(suffix) && suffix >= count) {
                    count = suffix + 1;
                }
            });
            let inputCount = document.getElementById('countinput');
            inputCount.value = count;

            //adding item logic
            const newContainer = document.createElement('div');
            newContainer.id = `repeatable-section-${count}`;
            newContainer.classList.add('row', 'card', 'p-3', 'mt-3');
            newContainer.innerHTML = `
    <div class="row">
    <div class="col-lg-2">
                              <label>Portal Mode</label>
                              <select name="portal_mode-${count}" id="portal_mode-${count}" class="form-control required select2" required>
                                  <option value="PORTAL">Portal</option>
                                  <option value="META">Meta</option>
                              </select>
                          </div>
    <div class="col-lg-2">
      <label>POS</label>
      <select name="pos-${count}" id="pos-${count}" class="form-control select2" required placeholder="POS">
        <option value="US">US</option>
        <option value="CA">CA</option>
      </select>
    </div>

    <div class="col-lg-2">
          <label>Portal</label>
              <select id="portal-${count}" class="form-select select2-primary" required name="portal-${count}">
                <?php $__currentLoopData = config('custom-app.portals'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $urls): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php $__currentLoopData = $urls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <option value="<?php echo e(str_replace('https://', '', $url)); ?>"><?php echo e(str_replace('https://', '', $url)); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
      </div>
      <div class="col-lg-2">
        <label>Content Sources</label>
                                <select id="content_sources-${count}" class="form-select select2" name="content_sources-${count}[]" multiple>
                                    <?php $__currentLoopData = $formatted_content_sources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>"><?php echo e($value); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
        </div>
         <div class="col-lg-2">
           <label>Fare Types</label>
          <select id="fareType-${count}" class="form-select select2-primary" required name="fare_type-${count}[]" multiple>
            <option value="Published" selected>Published</option>
            <option value="Private" selected>Private</option>
            <option value="Net" selected>Net</option>
          </select>
     </div>
        <div class="col-lg-2 text-end">
            <button class="btn btn-sm fs-5 btn-danger" type="button" onclick="removeItem(${count})">x</button>
        </div>
    </div>
    <!-- Origin Fieldset -->
    <div class="row">
    <div class="col-lg-4 mt-3">
      <label>Origin</label>
      <fieldset class="p-3 border rounded origin-group" label="Origin">
        <div class="d-flex mb-2">
          <div class="radio-container d-flex pr-3">
            <input type="radio" id="origin-group-radio-${count}" name="originRadio-${count}" value="1"
            class="originRadio form-check-input" onchange="handleOriginRadio(${count}, 1)" checked>
            <label class="mb-0 mx-1 form-label" >Group</label>
          </div>
          <div class="radio-container d-flex pr-3">
            <input type="radio" id="origin-country-radio-${count}" name="originRadio-${count}" value="2"
            class="originRadio form-check-input" onchange="handleOriginRadio(${count}, 2)">
            <label class="mb-0 mx-1 form-label">Country</label>
          </div>
          <div class="radio-container d-flex pr-3">
            <input type="radio" id="origin-airport-radio-${count}" name="originRadio-${count}" value="3"
            class="originRadio form-check-input" onchange="handleOriginRadio(${count}, 3)">
            <label class="mb-0 mx-1 form-label">Airport</label>
          </div>
        </div>

        <!-- Origin Group Container -->
        <div class="form-group origin-group-container" id="origin-group-container-${count}">
          <select rows="1" name="origin_group-${count}" class="form-control textBox-style w-100" placeholder="Enter location group" id="origin-group-${count}">
            <?php if(!empty($location_group)): ?>
                            <?php $__currentLoopData = $location_group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($item); ?>"><?php echo e($item); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
          </select>
        </div>

        <!-- Origin Country Container -->
        <div class="form-group origin-country-container" id="origin-country-container-${count}" style="display:none;">
          <textarea rows="1" name="origin_country-${count}" class="form-control textBox-style w-100" placeholder="Enter country" id="origin-country-${count}"></textarea>
        </div>

        <!-- Origin Airport Container -->
        <div class="form-group origin-airport-container" id="origin-airport-container-${count}" style="display:none;">
          <textarea rows="1" name="origin_airport-${count}" class="form-control textBox-style w-100" placeholder="Enter airports" id="origin-airport-${count}"></textarea>
        </div>
      </fieldset>
    </div>

    <!-- Destination Fieldset -->
    <div class="col-lg-4 mt-3">
      <label>Destination</label>
      <fieldset class="p-3 border rounded destination-group">
        <div class="d-flex mb-2">
          <div class="radio-container d-flex pr-3">
            <input type="radio" id="destination-group-radio-${count}" name="destinationRadio-${count}" value="4"
            class="destinationRadio form-check-input" onchange="handleDestinationRadio(${count}, 4)" checked>
            <label class="mb-0 mx-1 form-label">Group</label>
          </div>
          <div class="radio-container d-flex pr-3">
            <input type="radio" id="destination-country-radio-${count}" name="destinationRadio-${count}" value="5"
            class="destinationRadio form-check-input" onchange="handleDestinationRadio(${count}, 5)">
            <label class="mb-0 mx-1 form-label">Country</label>
          </div>
          <div class="radio-container d-flex pr-3">
            <input type="radio" id="destination-airport-radio-${count}" name="destinationRadio-${count}" value="6"
            class="destinationRadio form-check-input" onchange="handleDestinationRadio(${count}, 6)">
            <label class="mb-0 mx-1 form-label">Airport</label>
          </div>
        </div>

        <!-- Destination Group Container -->
        <div class="form-group destination-group-container" id="destination-group-container-${count}">
          <select rows="1" name="destination_group-${count}" class="form-control textBox-style w-100" placeholder="Enter location group" id="destination-group-${count}">
            <?php if(!empty($location_group)): ?>
                            <?php $__currentLoopData = $location_group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($item); ?>"><?php echo e($item); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
          </select>
        </div>

        <!-- Destination Country Container -->
        <div class="form-group destination-country-container" id="destination-country-container-${count}" style="display:none;">
          <textarea rows="1" name="destination_country-${count}" class="form-control textBox-style w-100" placeholder="Enter country" id="destination-country-${count}"></textarea>
        </div>

        <!-- Destination Airport Container -->
        <div class="form-group destination-airport-container" id="destination-airport-container-${count}" style="display:none;">
          <textarea rows="1" name="destination_airport-${count}" class="form-control textBox-style w-100" placeholder="Enter airports" id="destination-airport-${count}"></textarea>
        </div>
      </fieldset>
    </div>



                            <div class="col-md-2 mt-5">
                                <input type="checkbox" class="form-check-input" checked name="call_only_fare-${count}"
                                    id="call_only_fare-${count}">
                                Call Only Fares
                                <br>
                                <br>
                                  <input type="checkbox" class="form-check-input" name="booking-${count}" id="booking-${count}">
                                Booking
                            </div>
                            <div class="col-md-2 mt-5" id="booking_types-${count}">
                                <label>Booking Mode</label>
                                <select name="booking_type-${count}" id="booking_type-${count}" class="form-control required select2"
                                    required placeholder="booking type">
                                    <option value="gds">GDS</option>
                                    <option value="system">SYSTEM</option>
                                </select>
                            </div>
                             <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" checked name="show_carrier-${count}"
                                    id="show_carrier-${count}">
                                Show Carrier
                                <br>
                            </div>
                             <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" checked name="mask_validating_carrier-${count}"
                                    id="mask_validating_carrier-${count}">
                                Mask Validating Carrier
                                <br>
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" checked name="show_flight_timing-${count}"
                                    id="show_flight_timing-${count}">
                                Show Flight Timing
                                <br>
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" checked name="mask_marketing_carrier-${count}"
                                    id="mask_marketing_carrier-${count}">
                                Mask Marketing Carrier
                                <br>
                            </div>
                                
                       </div>

                    <div class="row mt-4">
                      <div class="col-lg-2">
                        <input type="checkbox" class="form-check-input" name="status-${count}" id="status-${count}" checked> Active
                      </div>



                    </div>`;

            document.getElementById('parent-container').appendChild(newContainer);
            handleAirlinesRadio(count, 8);

            const callOnlyFareCheckbox = document.getElementById(`call_only_fare-${count}`);
            const bookingCheckbox = document.getElementById(`booking-${count}`);
            const bookingTypeContainer = document.getElementById(`booking_types-${count}`);

            callOnlyFareCheckbox.checked = true;
            bookingCheckbox.checked = false;
            bookingTypeContainer.style.display = "none";

            callOnlyFareCheckbox.addEventListener("change", function() {
                if (this.checked) {
                    bookingCheckbox.checked = false;
                    bookingTypeContainer.style.display = "none";
                }
            });
            bookingCheckbox.addEventListener("change", function() {
                if (this.checked) {
                    callOnlyFareCheckbox.checked = false;
                    bookingTypeContainer.style.display = "block";
                } else {
                    bookingTypeContainer.style.display = "none";
                }
            });

            $(`#pos-${count}`).select2();
            $(`#portal-${count}`).select2();
            $(`#fareType-${count}`).select2();
            $(`#portal_mode-${count}`).select2();
            $(`#booking_type-${count}`).select2();
            $(`#content_sources-${count}`).select2();
        }

        function removeItem(count) {
            let elementToRemove = document.getElementById(`repeatable-section-${count}`);
            if (elementToRemove) {
                elementToRemove.remove();
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.layoutMaster', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/airline_masking/create.blade.php ENDPATH**/ ?>