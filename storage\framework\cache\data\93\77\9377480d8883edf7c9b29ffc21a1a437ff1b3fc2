9999999999O:25:"App\Models\AirlineMasking":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"airline_maskings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:10:{s:2:"id";i:1;s:11:"portal_mode";s:4:"META";s:3:"pos";s:2:"CA";s:6:"portal";s:15:"traveloplex.com";s:10:"fare_types";s:17:"Published,Private";s:16:"airlines_masking";s:334:"{"id":"1","POS":"CA","portal":"traveloplex.com","showCarrier":true,"maskValidatingCarrier":true,"showFlightTiming":true,"maskMarketingCarrier":true,"fareTypes":"Published,Private","locationGroup":{"from":{"ruleType":"country","country":"SR,CC"},"to":{"ruleType":"country","country":"ID"}},"bookingMode":"system","callOnlyFares":false}";s:6:"status";i:1;s:15:"content_sources";s:13:"scity1,scity4";s:10:"created_at";s:19:"2025-08-01 10:43:13";s:10:"updated_at";s:19:"2025-08-04 11:57:59";}s:11:" * original";a:10:{s:2:"id";i:1;s:11:"portal_mode";s:4:"META";s:3:"pos";s:2:"CA";s:6:"portal";s:15:"traveloplex.com";s:10:"fare_types";s:17:"Published,Private";s:16:"airlines_masking";s:334:"{"id":"1","POS":"CA","portal":"traveloplex.com","showCarrier":true,"maskValidatingCarrier":true,"showFlightTiming":true,"maskMarketingCarrier":true,"fareTypes":"Published,Private","locationGroup":{"from":{"ruleType":"country","country":"SR,CC"},"to":{"ruleType":"country","country":"ID"}},"bookingMode":"system","callOnlyFares":false}";s:6:"status";i:1;s:15:"content_sources";s:13:"scity1,scity4";s:10:"created_at";s:19:"2025-08-01 10:43:13";s:10:"updated_at";s:19:"2025-08-04 11:57:59";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:11:"portal_mode";i:1;s:3:"pos";i:2;s:6:"portal";i:3;s:15:"content_sources";i:4;s:10:"fare_types";i:5;s:15:"default_masking";i:6;s:16:"airlines_masking";i:7;s:6:"status";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}