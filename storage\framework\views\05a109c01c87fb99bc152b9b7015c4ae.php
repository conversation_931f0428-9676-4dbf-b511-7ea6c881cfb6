

<?php $__env->startSection('title', 'Content Sources'); ?>

<?php $__env->startSection('vendor-style'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
    <script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page-script'); ?>
    <script src="<?php echo e(asset('assets/js/contentsources/index.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="card header">
        <div class="card-body py-2">
            <div class="col-md-4 d-flex">
                <h5 class="mx-2 mt-2 text-white">Content Sources</h5>
                <div class="mx-2 mt-1">
                    <a href="<?php echo e(route('backend.content_sources.create')); ?>" class="btn btn-primary d-flex align-items-center">
                        <i class="ti ti-plus me-2" style="margin-top: -2px;"></i>
                        New
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body" data-masking-url="<?php echo e(route('backend.masking_master.index')); ?>">
            <?php if(session('success')): ?>
                <div class="alert alert-success" id="alertMessage">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger" id="alertMessage">
                    <?php echo e(session('error')); ?>

                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table AirlineMasking" id="AirlineMasking" style="width:100%">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Table Number</th>
                            <th>Original Carriers</th>
                            <th>Masked Name</th>
                            <th>Masked Code</th>
                            <th>Default Name</th>
                            <th>Default Code</th>
                            <th>Created At</th>
                            <th>Updated At</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.layoutMaster', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/content_sources/index.blade.php ENDPATH**/ ?>