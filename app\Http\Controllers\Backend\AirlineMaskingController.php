<?php

namespace App\Http\Controllers\backend;

use App\Http\Controllers\Controller;
use App\Models\AirlineMasking;
use App\Models\CarrierGroup;
use App\Models\GflightsGroup;
use App\Models\maskingMaster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class AirlineMaskingController extends Controller
{
  public function index(Request $request)
  {
      if ($request->ajax()) {
          $airlineMasking = AirlineMasking::orderBy('id', 'DESC')->get();
          
          return DataTables::of($airlineMasking)
              ->addIndexColumn()
              ->addColumn('actions', function ($row) {
                  $deleteUrl = route('backend.airline_masking.delete', ['id' => $row->id]);
                  $editUrl = route('backend.airline_masking.edit', ['id' => $row->id]);
                  
                  $btn = '<div class="dropdown">
                      <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                          <i class="ti ti-dots-vertical"></i>
                      </button>
                      <div class="dropdown-menu">
                          <a href="'.$editUrl.'" class="dropdown-item"><i class="ti ti-pencil me-1"></i>Edit</a>
                          <a href="'.$deleteUrl.'" class="dropdown-item delete"><i class="ti ti-trash me-1"></i>Delete</a>
                      </div>
                  </div>';
                  
                  return $btn;
              })
              ->addColumn('portal_mode', fn($row) => ucfirst($row->portal_mode))
              ->addColumn('from', function ($row) {
                  $airlines = json_decode($row->airlines_masking, true);
                  $from = $airlines['locationGroup']['from'] ?? [];
                  
                  if (!empty($from)) {
                      $ruleType = $from['ruleType'] ?? '';
                      $value = '';
                      
                      if ($ruleType === 'country') {
                          $value = $from['country'] ?? '';
                      } elseif ($ruleType === 'airport') {
                          $value = $from['airport'] ?? '';
                      }
                      
                      return ucfirst($ruleType) . ' : ' . $value;
                  }
                  return 'Any';
              })
              ->addColumn('to', function ($row) {
                  $airlines = json_decode($row->airlines_masking, true);
                  $to = $airlines['locationGroup']['to'] ?? [];
                  
                  if (!empty($to)) {
                      $ruleType = $to['ruleType'] ?? '';
                      $value = '';
                      
                      if ($ruleType === 'country') {
                          $value = $to['country'] ?? '';
                      } elseif ($ruleType === 'airport') {
                          $value = $to['airport'] ?? '';
                      }
                      
                      return ucfirst($ruleType) . ' : ' . $value;
                  }
                  return 'Any';
              })
              ->addColumn('fare_types', function($row) {
                  $airlines = json_decode($row->airlines_masking, true);
                  return ucfirst($airlines['fareTypes'] ?? '');
              })
              ->addColumn('status', function ($row) {
                  $checked = $row->status == 1 ? 'checked' : '';
                  return '<div class="form-check form-switch">
                      <input type="checkbox" class="form-check-input status-toggle" data-id="'.$row->id.'" '.$checked.'>
                  </div>';
              })
              ->rawColumns(['actions', 'status', 'from', 'to', 'fare_types'])
              ->make(true);
      }

      return view('backend.airline_masking.index');
  }


  public function updateStatus(Request $request)
  {
      $airline = AirlineMasking::findOrFail($request->id);
      $airline->status = $request->status;
      $airline->save();

      return response()->json(['message' => 'Status updated successfully']);
  }

  public function delete($id)
  {
      try {
          $airlineMasking = AirlineMasking::findOrFail($id);
          
          $airlineMasking->delete();
          
          return response()->json([
              'success' => true,
              'message' => 'Airline masking record deleted successfully'
          ]);
          
      } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
          return response()->json([
              'success' => false,
              'message' => 'Record not found'
          ], 404);
          
      } catch (\Exception $e) {
          return response()->json([
              'success' => false,
              'message' => 'Error deleting record: ' . $e->getMessage()
          ], 500);
      }
  }

  public function edit($id)
  {
      $airlineMasking = AirlineMasking::findOrFail($id);
    $maskingData = json_decode($airlineMasking->airlines_masking, true) ?? [];
    
    $content_sources = config('custom-app.content_sources_airline_masking');
    $formatted_content_sources = [];
    
    foreach ($content_sources as $key => $value) {
        $formatted_content_sources[$key] = $key . '-' . $value['pcc'];
    }
    
    // Get selected content sources
    $selected_content_sources = $airlineMasking->content_sources 
        ? explode(',', $airlineMasking->content_sources) 
        : [];
      
      
      // Provide default values for all possible keys with proper structure
      $defaults = [
          'showCarrier' => false,
          'doNotMaskingAirline' => false,
          'showFlightTiming' => false,
          'maskMarketingCarrier' => false,
          'POS' => 'US',
          'portal' => '',
          'fare_types' => '',
          'locationGroup' => [
              'from' => [
                  'ruleType' => '',
                  'country' => '',
                  'airport' => '',
                  'group' => ''
              ],
              'to' => [
                  'ruleType' => '',
                  'country' => '',
                  'airport' => '',
                  'group' => ''
              ]
          ],
          'bookingMode' => null,
          'callOnlyFares' => false
      ];

      // Deep merge the masking data with defaults
      $maskingData = array_replace_recursive($defaults, $maskingData);

      // Extract values for easy access in the view
      $originRuleType = $maskingData['locationGroup']['from']['ruleType'] ?? '';
      $originValue = '';
      if ($originRuleType && isset($maskingData['locationGroup']['from'][$originRuleType])) {
          $originValue = $maskingData['locationGroup']['from'][$originRuleType];
      }

      $destinationRuleType = $maskingData['locationGroup']['to']['ruleType'] ?? '';
      $destinationValue = '';
      if ($destinationRuleType && isset($maskingData['locationGroup']['to'][$destinationRuleType])) {
          $destinationValue = $maskingData['locationGroup']['to'][$destinationRuleType];
      }

      // Load airport and airline data
      $airport_json = json_decode(file_get_contents(public_path() . "/resources/airports.json"), true);
      $airline_json = json_decode(file_get_contents(public_path() . "/resources/airlines.json"), true);

      $airport = [];
      $raw_country = [];
      $country = [];
      $airline = [];

      foreach ($airport_json as $json_value) {
          $parts = preg_split("/[|]/", $json_value['value']);
          $airport[] = [
              'code' => $json_value['id'],
              'name' => $json_value['id'] . "-" . $parts[1],
              'country_code' => $parts[5],
              'country_name' => $parts[6]
          ];
          $raw_country[] = $parts[5] . '-' . $parts[6];
      }

      foreach ($airline_json as $json_value) {
          $airline[] = [
              'code' => $json_value['id'],
              'name' => $json_value['name']
          ];
      }

      $country = array_values(array_unique($raw_country));
      $location_group = GflightsGroup::pluck('locationgroup_name')->toArray();
      $carrier_group = CarrierGroup::pluck('carriergroup_name')->toArray();

     
    // echo '<pre>'; print_r($airlineMasking); echo '</pre>'; return;


      return view('backend.airline_masking.edit', compact(
          'airlineMasking',
          'maskingData',
          'airport',
          'country',
          'location_group',
          'carrier_group',
          'airline',
          'originRuleType',
          'originValue',
          'destinationRuleType',
          'destinationValue',
          'formatted_content_sources',
          'selected_content_sources'
      ));
  }
  public function update(Request $request, $id)
  {
      try {
          $airlineMasking = AirlineMasking::findOrFail($id);
          
          $marketingCarrier = null;
          $validatingCarrier = null;
          $from_ruletype = null;
          $to_rule_type = null;

          $from_list = null;
          $to_list = null;
          $blocked_list = null;

          $showCarrier = $request->input('show_carrier-0') === "on";
          $maskValidatingCarrier = $request->input('mask_validating_carrier-0') === "on";
          $showFlightTiming = $request->input('show_flight_timing-0') === "on";
          $maskMarketingCarrier = $request->input('mask_marketing_carrier-0') === "on";
          $pos = strtoupper($request->input('pos-0'));
          $portal_mode = strtoupper($request->input('portal_mode-0'));

          $content_sources = !empty($request->input('content_sources-0')) 
            ? implode(',', $request->input('content_sources-0')) 
            : '';

          if ($request->input('originRadio-0') == "1") {
              $from_ruletype = "group";
              $from_list = $request->input('origin_group-0');

              // Store the group name directly instead of converting to airport/city/country
              // The group name will be used for matching in the edit view
          } else if ($request->input('originRadio-0') == "2") {
              $from_ruletype = "country";
              $countryList = json_decode($request->input('origin_country-0'));
              $arr = [];

              if ($countryList !== NULL) {
                  foreach ($countryList as $country) {
                      $altered_value = explode('-', $country->value)[0];
                      array_push($arr, $altered_value);
                  }
                  $from_list = implode(',', $arr);
              } else {
                  $from_list = null;
              }
          } else if ($request->input('originRadio-0') == "3") {
              $from_ruletype = "airport";

              $airportList = json_decode($request->input('origin_airport-0'));
              $arr = [];
              if ($airportList !== Null) {
                  foreach ($airportList as $airport) {
                      $altered_value = explode('-', $airport->value)[0];
                      array_push($arr, $altered_value);
                  }
                  $from_list = implode(',', $arr);
              } else {
                  $from_list = null;
              }
          }

          //destination
          if ($request->input('destinationRadio-0') == "4") {
              $to_rule_type = "group";
              $to_list = $request->input('destination_group-0');

              // Store the group name directly instead of converting to airport/city/country
              // The group name will be used for matching in the edit view
          } else if ($request->input('destinationRadio-0') == "5") {
              $to_rule_type = "country";

              $countryList = json_decode($request->input('destination_country-0'));
              $arr = [];
              if ($countryList !== null) {
                  foreach ($countryList as $country) {
                      $altered_value = explode('-', $country->value)[0];
                      array_push($arr, $altered_value);
                  }
                  $to_list = implode(',', $arr);
              } else {
                  $to_list = null;
              }
          } else if ($request->input('destinationRadio-0') == "6") {
              $to_rule_type = "airport";

              if ($request->input('destination_airport-0') == null || $request->input('destination_airport-0') == '') {
                  session()->flash('error', 'invalid input data');
                  return redirect()->route('backend.airline_masking.edit', $id);
              }

              $airportList = json_decode($request->input('destination_airport-0'));
              $arr = [];
              if ($airportList !== null) {
                  foreach ($airportList as $airport) {
                      $altered_value = explode('-', $airport->value)[0];
                      array_push($arr, $altered_value);
                  }
                  $to_list = implode(',', $arr);
              } else {
                  $to_list = null;
              }
          }

          $fareTypes = !empty($request->input('fare_type-0')) ? implode(',', $request->input('fare_type-0')) : '';
          $portal = !empty($request->input('portal-0')) ? $request->input('portal-0') : '';
          $from = [
              "ruleType" => $from_ruletype,
              $from_ruletype => $from_list,
          ];
          $to = [
              "ruleType" => $to_rule_type,
              $to_rule_type => $to_list,
          ];

          $json_node = [
            "id" => $id,
            "POS" => $pos,
            "portal" => $portal,
            "showCarrier" => $showCarrier,
            "maskValidatingCarrier" => $maskValidatingCarrier,
            "showFlightTiming" => $showFlightTiming,
            "maskMarketingCarrier" => $maskMarketingCarrier,
            "fareTypes" => $fareTypes,
            "locationGroup" => [
                "from" => isset($from_list) ? $from : [],
                "to" => isset($to_list) ? $to : [],
            ],
            "bookingMode" => ($request->input('booking-0') !== null && $request->input('booking-0') == 'on') 
                            ? $request->input('booking_type-0') 
                            : null,
            "callOnlyFares" => ($request->input('call_only_fare-0') !== null) 
                            ? ($request->input('call_only_fare-0') == 'on' ? true : false) 
                            : false,
        ];


          if (blank($request->input('portal_mode-0'))) {
              return redirect()->route('backend.airline_masking.edit', $id)->with('error', 'Select any portal');
          }

          $airlineMasking->update([
              'portal_mode' => $portal_mode,
              'pos' => $pos,
              'portal' => $portal,
              'fare_types' => $fareTypes,
              'content_sources' => $content_sources, 
              'airlines_masking' => json_encode($json_node),
              'status' => ($request->input('status-0')) !== null ? ($request->input('status-0') == 'on' ? 1 : 0) : 0,
          ]);

          cache::put(config('custom-app.citiaircache'),$airlineMasking);

          session()->flash('success', 'Airline masking updated successfully');
          return redirect()->route('backend.airline_masking.index');
      } catch (\Exception $e) {
          session()->flash('error', $e->getMessage() . " -Failed to update airline masking");
          return redirect()->route('backend.airline_masking.edit', $id);
      }
  }



  public function create()
  {
      $airport_json = json_decode(file_get_contents(public_path() . "/resources/airports.json"), true);
      $airline_json = json_decode(file_get_contents(public_path() . "/resources/airlines.json"), true);

      $airport = array();
      $raw_country = array();
      $country = array();
      $airline = array();
      $content_sources = config('custom-app.content_sources_airline_masking');
      $formatted_content_sources = [];

      foreach ($content_sources as $key => $value) {
          $formatted_content_sources[$key] = $key . '-' . $value['pcc'];
      }

      foreach ($airport_json as $json_value) {
          $parts = preg_split("/[|]/", $json_value['value']);
          array_push($airport, ["code" => $json_value['id'], "name" => $json_value['id'] . "-" . $parts[1]]);
          array_push($raw_country, $parts[5] . '-' . $parts[6]);
      }

      foreach ($airline_json as $json_value) {
          array_push($airline, ["code" => $json_value['id'], "name" => $json_value['name']]);
      }
      $country = array_values(array_unique($raw_country));

      $location_group = GflightsGroup::pluck('locationgroup_name')->toArray();
      $carrier_group = CarrierGroup::pluck('carriergroup_name')->toArray();

      return view('backend.airline_masking.create', compact('airport', 'country', 'location_group', 'carrier_group', 'airline', 'formatted_content_sources'));
  }


  public function store(Request $request)
  {
    // dd($request);
    try {
      $count_index = $request->input('count') ?? 0;

      for ($i = 0; $i <= intval($count_index); $i++) {
        $id = $i + 1;
        $marketingCarrier = null;
        $validatingCarrier = null;
        $from_ruletype = null;
        $to_rule_type = null;

        $from_list = null;
        $to_list = null;
        $blocked_list = null;

        $showCarrier = $request->input('show_carrier-' . $i) === "on";
        $maskValidatingCarrier = $request->input('mask_validating_carrier-' . $i) === "on";
        $showFlightTiming = $request->input('show_flight_timing-' . $i) === "on";
        $maskMarketingCarrier = $request->input('mask_marketing_carrier-' . $i) === "on";
        $pos = strtoupper($request->input('pos-' . $i));
        $portal_mode = strtoupper($request->input('portal_mode-' . $i));

         $content_sources = !empty($request->input('content_sources-'.$i)) 
                ? implode(',', $request->input('content_sources-'.$i)) 
                : '';

        if ($request->input('originRadio-' . $i) == "1") {
          $from_ruletype = "group";
          $from_list = $request->input('origin_group-' . $i);

          // Store the group name directly instead of converting to airport/city/country
          // The group name will be used for matching in the edit view
        } else if ($request->input('originRadio-' . $i) == "2") {
          $from_ruletype = "country";
          $countryList = json_decode($request->input('origin_country-' . $i));
          $arr = [];

          if ($countryList !== NULL) {
            foreach ($countryList as $country) {
              $altered_value = explode('-', $country->value)[0];
              array_push($arr, $altered_value);
            }
            $from_list = implode(',', $arr);
          } else {
            $from_list = null;
          }
        } else if ($request->input('originRadio-' . $i) == "3") {
          $from_ruletype = "airport";

          $airportList = json_decode($request->input('origin_airport-' . $i));
          $arr = [];
          if ($airportList !== Null) {
            foreach ($airportList as $airport) {
              $altered_value = explode('-', $airport->value)[0];
              array_push($arr, $altered_value);
            }
            $from_list = implode(',', $arr);
          } else {
            $from_list = null;
          }
        }


        //destination
        if ($request->input('destinationRadio-' . $i) == "4") {
          $to_rule_type = "group";
          $to_list = $request->input('destination_group-' . $i);

          // Store the group name directly instead of converting to airport/city/country
          // The group name will be used for matching in the edit view
        } else if ($request->input('destinationRadio-' . $i) == "5") {
          $to_rule_type = "country";


          $countryList = json_decode($request->input('destination_country-' . $i));
          $arr = [];
          if ($countryList !== null) {
            foreach ($countryList as $country) {
              $altered_value = explode('-', $country->value)[0];
              array_push($arr, $altered_value);
            }
            $to_list = implode(',', $arr);
          } else {
            $to_list = null;
          }
        } else if ($request->input('destinationRadio-' . $i) == "6") {
          $to_rule_type = "airport";

          if ($request->input('destination_airport-' . $i) == null || $request->input('destination_airport-' . $i) == '') {
            session()->flash('error', 'invalid input data');
            return redirect()->route('backend.airline_masking.create');
          }

          $airportList = json_decode($request->input('destination_airport-' . $i));
          $arr = [];
          if ($airportList !== null) {
            foreach ($airportList as $airport) {
              $altered_value = explode('-', $airport->value)[0];
              array_push($arr, $altered_value);
            }
            $to_list = implode(',', $arr);
          } else {
            $to_list = null;
          }
        }

        $fareTypes = !empty($request->input('fare_type-' . $i)) ? implode(',', $request->input('fare_type-' . $i)) : '';
        $portal = !empty($request->input('portal-' . $i)) ? $request->input('portal-' . $i) : '';
        $from = [
          "ruleType" => $from_ruletype,
          $from_ruletype => $from_list,
        ];
        $to = [
          "ruleType" => $to_rule_type,
          $to_rule_type => $to_list,
        ];


        $json_node = [
          "id" => $id,
          "POS" => $pos,
          "portal" => $portal,
          "showCarrier" => $showCarrier,
          "maskValidatingCarrier" => $maskValidatingCarrier,
          "showFlightTiming" => $showFlightTiming,
          "maskMarketingCarrier" => $maskMarketingCarrier,
          "fareTypes" => $fareTypes,
          "locationGroup" => [
            "from" => isset($from_list) ? $from : [],
            "to" => isset($to_list) ? $to : [],
          ],
          "bookingMode" => (($request->input('booking-' . $i) !== null) && ($request->input('booking-' . $i) == 'on')) ? $request->input('booking_type-' . $i) :  null,
          "callOnlyFares" => ($request->input('call_only_fare-' . $i)) !== null ? ($request->input('call_only_fare-' . $i) == 'on' ? true : false) : false,
        ];


        if (blank($request->input('portal_mode-' . $i))) {
          return redirect()->route('backend.airline_masking.create')->with('error', 'Select any portal');
        }

        $val = AirlineMasking::create([
          'portal_mode' => $portal_mode,
          'pos' => $pos,
          'portal' => $portal,
          'content_sources' => $content_sources,
          'fare_types' => $fareTypes,
          'airlines_masking' => json_encode($json_node),
          'status' => ($request->input('status-' . $i)) !== null ? ($request->input('status-' . $i) == 'on' ? 1 : 0) : 0,
        ]);

        cache::put(config('custom-app.citiaircache'),$val);
    
      }
      session()->flash('success', 'Airline blockings created successfully');
      return redirect()->route('backend.airline_masking.index');
    } catch (\Exception $e) {
      return response()->json(['message' => $e->getMessage(), 'status' => 'failed'], 500);
      session()->flash('error', $e->getMessage() . " -Failed to create airline blocks");
      return redirect()->route('backend.airline_masking.create');
    }
  }

  // masking group
public function indexMaster(Request $request)
{
    if ($request->ajax()) {
        $maskingGroups = MaskingMaster::select('id', 'table_num', 'airline_carrier', 'masked_name', 'masked_code', 'default_name', 'default_code', 'is_active', 'created_at', 'updated_at')
            ->orderBy('id', 'DESC')
            ->get();

        return DataTables::of($maskingGroups)
            ->addIndexColumn()
            ->addColumn('airline_carriers', function ($row) {
                return implode(', ', explode(',', $row->airline_carrier));
            })
            ->addColumn('actions', function ($row) {
                $deleteUrl = route('backend.masking_master.delete', ['id' => $row->id]);
                $editUrl = route('backend.masking_master.edit', ['id' => $row->id]);
                
                $btn = '<div class="dropdown">
                    <button type="button" class="btn p-0 dropdown-toggle hide-arrow" data-bs-toggle="dropdown">
                        <i class="ti ti-dots-vertical"></i>
                    </button>
                    <div class="dropdown-menu">
                        <a href="'.$editUrl.'" class="dropdown-item"><i class="ti ti-pencil me-1"></i>Edit</a>
                        <a href="'.$deleteUrl.'" class="dropdown-item delete"><i class="ti ti-trash me-1"></i>Delete</a>
                    </div>
                </div>';
                
                return $btn;
            })
            ->addColumn('created_at', function($row) {
                return $row->created_at->format('Y-m-d H:i:s'); 
            })
            ->addColumn('updated_at', function($row) {
                return $row->updated_at->format('Y-m-d H:i:s'); 
            })
            ->addColumn('status', function ($row) {
                $checked = $row->is_active ? 'checked' : '';
                return '<div class="form-check form-switch">
                    <input type="checkbox" class="form-check-input status-toggle" data-id="'.$row->id.'" '.$checked.'>
                </div>';
            })
            ->rawColumns(['actions', 'status'])
            ->make(true);
    }

    return view('backend.airline_masking_group.index');
}

public function updateStatusMaster(Request $request, $id)
{
    try {
        $masking = MaskingMaster::findOrFail($id);
        $masking->is_active = $request->status;
        $masking->save();
        
        return response()->json(['success' => true, 'message' => 'Status updated successfully.']);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
    }
}

public function destroyMaster($id)
{
    try {
        $masking = MaskingMaster::findOrFail($id);
        $masking->delete();
        
        return response()->json(['success' => true]);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
    }
}

 public function editMaster($id)
{
    try {
        $airline_json = json_decode(file_get_contents(public_path() . "/resources/airlines.json"), true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("Failed to parse airlines.json: " . json_last_error_msg());
        }
        
        $maskingData = MaskingMaster::findOrFail($id);

        // Get all active airlines already in use (excluding soft deleted ones)
        $usedAirlines = MaskingMaster::whereNull('deleted_at')
            ->where('id', '!=', $id) // exclude current record
            ->pluck('airline_carrier')
            ->flatMap(function($carriers) {
                return explode(',', $carriers);
            })
            ->unique()
            ->filter()
            ->all();

        // Prepare airlines data, excluding already used ones
        $airlines = collect();
        foreach ($airline_json as $json_value) {
            if (!empty($json_value['id']) && !empty($json_value['name'])) {
                // Only include if not already used by another record
                if (!in_array($json_value['id'], $usedAirlines)) {
                    $airlines->push([
                        "code" => $json_value['id'],
                        "name" => $json_value['name']
                    ]);
                }
            }
        }

        // Prepare selected airlines (should include even if they're in usedAirlines)
        $selectedAirlines = [];
        $carriers = explode(',', $maskingData->airline_carrier);
        foreach ($carriers as $carrier) {
            $airline = collect($airline_json)->firstWhere('id', $carrier);
            if ($airline) {
                $selectedAirlines[] = [
                    'value' => $airline['id'] . '-' . $airline['name'],
                    'id' => $airline['id']
                ];
            }
        }

        // Prepare data for view
        $defaultData = [
            'default_masked_name' => $maskingData->default_name,
            'default_masked_code' => $maskingData->default_code,
            'default_logo_name' => $maskingData->default_logo,
            'default_logo_url' => $maskingData->default_logo ? 
                asset(config('custom-app.masking_log_path') . '/' . $maskingData->default_logo) : null
        ];

        $airlineData = [
            'masked_name' => $maskingData->masked_name,
            'masked_code' => $maskingData->masked_code,
            'logo_name' => $maskingData->masked_logo,
            'logo_url' => $maskingData->masked_logo ? 
                asset(config('custom-app.masking_log_path') . '/' . $maskingData->masked_logo) : null,
            'is_active' => $maskingData->is_active,
            'selected_airlines' => $selectedAirlines
        ];

        return view('backend.airline_masking_group.edit', compact(
            'airlines',
            'maskingData',
            'defaultData',
            'airlineData',
            'id'
        ));
        
    } catch (\Exception $e) {
        Log::error('Error in editMaster: ' . $e->getMessage());
        return back()->with('error', 'Failed to load airline data: ' . $e->getMessage());
    }
}

public function updateMaster(Request $request, $id)
{
    $validated = $request->validate([
        'default_masked_name' => 'required|string|max:255',
        'default_masked_code' => 'required|string|max:255',
        'default_masked_logo' => 'nullable|image|mimes:jpeg,png,svg,webp|max:2048',
    ]);

    $maskingLogoPath = config('custom-app.masking_log_path');

    DB::beginTransaction();
    try {
        $existing = MaskingMaster::find($id);
        if (!$existing) {
            throw new \Exception("Invalid MaskingMaster ID: $id");
        }

        $tableNum = $existing->table_num;

        $existing->delete();

        $defaultCode = $validated['default_masked_code'];
        $defaultName = $validated['default_masked_name'];
        $defaultLogoName = null;

        if ($request->hasFile('default_masked_logo')) {
            $defaultLogoFile = $request->file('default_masked_logo');
            $defaultLogoName = $defaultCode . '.' . $defaultLogoFile->getClientOriginalExtension();
            $defaultLogoFile->storeAs($maskingLogoPath, $defaultLogoName, 'public');
        } elseif ($request->input('existing_default_logo')) {
            $defaultLogoName = $request->input('existing_default_logo');
        }

        $count_index = intval($request->input('count', 0));

        for ($i = 0; $i <= $count_index; $i++) {
            $airlineRaw = $request->input("airlines_airport-$i");

            if ($airlineRaw) {
                $decoded = json_decode($airlineRaw, true);
                if (!empty($decoded)) {
                    $airlineCodes = array_map(function ($item) {
                        return $item['id'] ?? null;
                    }, $decoded);

                    $airlineCarriers = implode(',', array_filter($airlineCodes));

                    $request->validate([
                        "masked_name-$i" => 'required|string|max:255',
                        "masked_code-$i" => 'required|string|max:255',
                        "masked_logo-$i" => 'nullable|image|mimes:jpeg,png,svg,webp|max:2048',
                    ]);

                    $maskedName = $request->input("masked_name-$i");
                    $maskedCode = $request->input("masked_code-$i");
                    $isActive = $request->input("status-$i") === "on" ? 1 : 0;

                    $maskedLogoName = null;
                    if ($request->hasFile("masked_logo-$i")) {
                        $maskedLogoFile = $request->file("masked_logo-$i");
                        $maskedLogoName = $maskedCode . '.' . $maskedLogoFile->getClientOriginalExtension();
                        $maskedLogoFile->storeAs($maskingLogoPath, $maskedLogoName, 'public');
                    } elseif ($request->input("existing_masked_logo-$i")) {
                        $maskedLogoName = $request->input("existing_masked_logo-$i");
                    }

                    MaskingMaster::create([
                        'table_num'       => $tableNum,
                        'airline_carrier' => $airlineCarriers,
                        'masked_name'     => $maskedName,
                        'masked_code'     => $maskedCode,
                        'masked_logo'     => $maskedLogoName,
                        'default_name'    => $defaultName,
                        'default_code'    => $defaultCode,
                        'default_logo'    => $defaultLogoName,
                        'is_active'       => $isActive,
                        'created_at'      => now(),
                        'updated_at'      => now()
                    ]);
                } else {
                    throw new \Exception("Invalid airline data format for item $i");
                }
            }
        }

        DB::commit();
        return redirect()->route('backend.masking_master.index')->with('success', 'Airline masking updated successfully!');
    } catch (\Exception $e) {
        DB::rollBack();
        Log::error('updateMaster error: ' . $e->getMessage());
        return redirect()->back()
            ->with('error', 'Something went wrong while updating data: ' . $e->getMessage())
            ->withInput();
    }
}


    public function createMaster()
    {
        try {
            $airline_json = json_decode(file_get_contents(public_path() . "/resources/airlines.json"), true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception("Failed to parse airlines.json: " . json_last_error_msg());
            }
            
            $existingAirlines = MaskingMaster::whereNull('deleted_at')
                ->pluck('airline_carrier')
                ->flatMap(function($carriers) {
                    return explode(',', $carriers);
                })
                ->unique()
                ->filter()
                ->toArray();
            
            $airline = [];
            foreach ($airline_json as $json_value) {
                if (empty($json_value['id']) || empty($json_value['name'])) {
                    continue;
                }
                
                if (!in_array($json_value['id'], $existingAirlines)) {
                    $airline[] = [
                        "code" => $json_value['id'],
                        "name" => $json_value['name']
                    ];
                }
            }

            return view('backend.airline_masking_group.create', compact('airline'));
            
        } catch (\Exception $e) {
            Log::error('Error in createMaster: ' . $e->getMessage());
            return back()->with('error', 'Failed to load airline data: ' . $e->getMessage());
        }
    }


    public function storeMaster(Request $request)
    {
        $validated = $request->validate([
            'default_masked_name' => 'required|string|max:255',
            'default_masked_code' => 'required|string|max:255',
            'default_masked_logo' => 'nullable|image|mimes:jpeg,png,svg,webp|max:2048',
        ]);

        $maskingLogoPath = config('custom-app.masking_log_path');
        $tableNo = rand(10000, 99999);
        
        DB::beginTransaction();
        try {
            // Handle default values
            $defaultCode = $validated['default_masked_code'];
            $defaultName = $validated['default_masked_name'];
            $defaultLogoName = null;
            
            if ($request->hasFile('default_masked_logo')) {
                $defaultLogoFile = $request->file('default_masked_logo');
                $defaultLogoName = $defaultCode . '.' . $defaultLogoFile->getClientOriginalExtension();
                $defaultLogoFile->storeAs($maskingLogoPath, $defaultLogoName, 'public');
            }
            
            // Handle individual airlines groups
            $count_index = intval($request->input('count', 0));

            // Track all selected airlines to prevent duplicates
            $allSelectedAirlines = [];

            for ($i = 0; $i <= $count_index; $i++) {
                $airlineRaw = $request->input("airlines_airport-$i");
                
                if ($airlineRaw) {
                    $decoded = json_decode($airlineRaw, true);
                    if (!empty($decoded)) {
                        // Extract all airline codes and join them with commas
                        $airlineCodes = array_map(function($item) {
                            return $item['id'] ?? null;
                        }, $decoded);

                        $airlineCodes = array_filter($airlineCodes);

                        // Check for duplicate airlines within this form submission
                        foreach ($airlineCodes as $airlineCode) {
                            if (in_array($airlineCode, $allSelectedAirlines)) {
                                throw new \Exception("Duplicate airline detected: $airlineCode. Each airline can only be selected once per masking group.");
                            }
                            $allSelectedAirlines[] = $airlineCode;
                        }

                        $airlineCarriers = implode(',', $airlineCodes);
                        
                        // Validate masked values for this group
                        $request->validate([
                            "masked_name-$i" => 'required|string|max:255',
                            "masked_code-$i" => 'required|string|max:255',
                            "masked_logo-$i" => 'nullable|image|mimes:jpeg,png,svg,webp|max:2048',
                        ]);
                        
                        $maskedName = $request->input("masked_name-$i");
                        $maskedCode = $request->input("masked_code-$i");
                        $isActive = $request->input("status-$i") === "on" ? 1 : 0;
                        
                        $maskedLogoName = null;
                        if ($request->hasFile("masked_logo-$i")) {
                            $maskedLogoFile = $request->file("masked_logo-$i");
                            $maskedLogoName = $maskedCode . '.' . $maskedLogoFile->getClientOriginalExtension();
                            $maskedLogoFile->storeAs($maskingLogoPath, $maskedLogoName, 'public');
                        }
                        
                        // Create one record per group with all airline carriers
                        MaskingMaster::create([
                            'table_num' => $tableNo,
                            'airline_carrier' => $airlineCarriers,
                            'masked_name' => $maskedName,
                            'masked_code' => $maskedCode,
                            'masked_logo' => $maskedLogoName,
                            'default_name' => $defaultName,
                            'default_code' => $defaultCode,
                            'default_logo' => $defaultLogoName,
                            'is_active' => $isActive,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                    } else {
                        throw new \Exception("Invalid airline data format for item $i");
                    }
                }
            }
            
            DB::commit();
            return redirect()->back()->with('success', 'Airline masking saved successfully!');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('storeMaster error: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', 'Something went wrong while saving data: ' . $e->getMessage())
                ->withInput();
        }
    }
}
