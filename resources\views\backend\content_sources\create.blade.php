@extends('layouts.layoutMaster')

@section('title', 'Create Content Source')

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/tagify/tagify.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/typeahead-js/typeahead.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/tagify/tagify.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/js/forms-typeahead.js') }}"></script>
@endsection

@section('content')
    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif



    <div class="card">
        <div class="card-body" style="padding:20px 35px;">
            <div class="row mb-4">
                <div class="col-11">
                    <h5>Content Source</h5>
                </div>
            </div>

            <form method="POST" action="{{ route('backend.content_sources.store') }}" enctype="multipart/form-data" id="content-source-form">
                @csrf
                
                <!-- Basic Information Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">Basic Information</h6>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                        @error('name')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="identifier" class="form-label">Identifier <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('identifier') is-invalid @enderror" id="identifier" name="identifier" 
                               value="{{ old('identifier') }}" maxlength="15" pattern="^\S+$" title="No spaces allowed" required>
                        <small class="text-muted">Unique ID, NO SPACES, 15 characters max</small>
                        @error('identifier')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="content_provider" class="form-label">Content Provider</label>
                        <input type="text" class="form-control @error('content_provider') is-invalid @enderror" id="content_provider" name="content_provider" value="{{ old('content_provider') }}">
                        @error('content_provider')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="end_txn_received_from" class="form-label">End Txn. Received From</label>
                        <input type="text" class="form-control @error('end_txn_received_from') is-invalid @enderror" id="end_txn_received_from" name="end_txn_received_from" value="<?php echo $received_from ?>" readonly>
                        @error('end_txn_received_from')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Agency Address Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">Agency Address</h6>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="address_line1" class="form-label">Address Line 1</label>
                        <input type="text" class="form-control @error('address_line1') is-invalid @enderror" id="address_line1" name="address_line1" value="{{ old('address_line1') }}">
                        @error('address_line1')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="address_line2" class="form-label">Address Line 2</label>
                        <input type="text" class="form-control @error('address_line2') is-invalid @enderror" id="address_line2" name="address_line2" value="{{ old('address_line2') }}">
                        @error('address_line2')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="city" class="form-label">City</label>
                        <input type="text" class="form-control @error('city') is-invalid @enderror" id="city" name="city" value="{{ old('city') }}">
                        @error('city')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="state" class="form-label">State</label>
                        <input type="text" class="form-control @error('state') is-invalid @enderror" id="state" name="state" value="{{ old('state') }}">
                        @error('state')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="postalcode" class="form-label">Postal Code</label>
                        <input type="text" class="form-control @error('postalcode') is-invalid @enderror" id="postalcode" name="postalcode" value="{{ old('postalcode') }}">
                        @error('postalcode')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label for="country" class="form-label">Country</label>
                        <select class="form-select country-select2 @error('country') is-invalid @enderror" id="country" name="country">
                            <option value="">Select Country</option>
                            <!-- Countries will be loaded via JavaScript -->
                        </select>
                        @error('country')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- GDS Configuration Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">GDS Configuration</h6>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="product" class="form-label">Product</label>
                        <select class="form-select product-select2 @error('product') is-invalid @enderror" id="product" name="product">
                            <option value="">Select Product</option>
                            @foreach($products as $key => $value)
                                <option value="{{ $key }}" {{ old('product') == $key ? 'selected' : '' }}>{{ $value }}</option>
                            @endforeach
                        </select>
                        @error('product')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="gds_pos_country" class="form-label">GDS POS Country</label>
                        <select class="form-select country-select2 @error('gds_pos_country') is-invalid @enderror" id="gds_pos_country" name="gds_pos_country">
                            <option value="">Select Country</option>
                            <!-- Countries will be loaded via JavaScript -->
                        </select>
                        @error('gds_pos_country')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="gds_timezone" class="form-label">GDS Timezone</label>
                        <select class="form-select timezone-select2 @error('gds_timezone') is-invalid @enderror" id="gds_timezone" name="gds_timezone">
                            <option value="">Select Timezone</option>
                            <!-- Timezones will be loaded via JavaScript -->
                        </select>
                        @error('gds_timezone')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="gds" class="form-label">GDS</label>
                        <select class="form-select gds-select2 @error('gds') is-invalid @enderror" id="gds" name="gds">
                            <option value="">Select GDS</option>
                            <option value="1S" {{ old('gds') == '1S' ? 'selected' : '' }}>1S - Sabre</option>
                            <option value="1A" {{ old('gds') == '1A' ? 'selected' : '' }}>1A - Amadeus</option>
                            <option value="1G" {{ old('gds') == '1G' ? 'selected' : '' }}>1G - Galileo</option>
                            <option value="1P" {{ old('gds') == '1P' ? 'selected' : '' }}>1P - Worldspan</option>
                            <option value="1V" {{ old('gds') == '1V' ? 'selected' : '' }}>1V - Apollo</option>
                            <option value="1B" {{ old('gds') == '1B' ? 'selected' : '' }}>1B - Abacus</option>
                            <option value="1F" {{ old('gds') == '1F' ? 'selected' : '' }}>1F - Farelogix</option>
                            <option value="1W" {{ old('gds') == '1W' ? 'selected' : '' }}>1W - 1ASEAT</option>
                        </select>
                        @error('gds')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="gds_version" class="form-label">GDS Version</label>
                        <input type="text" class="form-control @error('gds_version') is-invalid @enderror" id="gds_version" name="gds_version" value="{{ old('gds_version') }}">
                        @error('gds_version')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="api_method" class="form-label">API Method</label>
                        <select class="form-select api-method-select2 @error('api_method') is-invalid @enderror" id="api_method" name="api_method">
                            <option value="">Select API Method</option>
                            <option value="SOAP_XML" {{ old('api_method') == 'SOAP_XML' ? 'selected' : '' }}>SOAP XML</option>
                            <option value="REST_JSON" {{ old('api_method') == 'REST_JSON' ? 'selected' : '' }}>REST JSON</option>
                            <option value="GraphQL" {{ old('api_method') == 'GraphQL' ? 'selected' : '' }}>GraphQL</option>
                            <option value="gRPC" {{ old('api_method') == 'gRPC' ? 'selected' : '' }}>gRPC</option>
                            <option value="WebSocket" {{ old('api_method') == 'WebSocket' ? 'selected' : '' }}>WebSocket</option>
                            <option value="FTP" {{ old('api_method') == 'FTP' ? 'selected' : '' }}>FTP</option>
                            <option value="SFTP" {{ old('api_method') == 'SFTP' ? 'selected' : '' }}>SFTP</option>
                        </select>
                        @error('api_method')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="gds_currency" class="form-label">GDS Currency</label>
                        <select class="form-select currency-select2 @error('gds_currency') is-invalid @enderror" id="gds_currency" name="gds_currency">
                            <option value="">Select Currency</option>
                            <!-- Currencies will be loaded via JavaScript -->
                        </select>
                        @error('gds_currency')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="allowed_currencies" class="form-label">Allowed Currencies</label>
                        <textarea rows="1" class="form-control currency-tagify @error('allowed_currencies') is-invalid @enderror"
                                  id="allowed_currencies" name="allowed_currencies"
                                  placeholder="Search and select currencies">{{ old('allowed_currencies') }}</textarea>
                        @error('allowed_currencies')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="dk_number" class="form-label">DK Number</label>
                        <div class="input-group">
                            <input type="text" class="form-control @error('dk_number') is-invalid @enderror" id="dk_number" name="dk_number" value="{{ old('dk_number') }}">
                            <div class="input-group-text">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="allow_override" name="allow_override" checked>
                                    <label class="form-check-label" for="allow_override">Allow Override</label>
                                </div>
                            </div>
                        </div>
                        @error('dk_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="pcc_office_id" class="form-label" id="pcc_office_label">PCC / Office ID</label>
                        <input type="text" class="form-control @error('pcc_office_id') is-invalid @enderror" id="pcc_office_id" name="pcc_office_id" value="{{ old('pcc_office_id') }}" readonly>
                        @error('pcc_office_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label for="api_cache" class="form-label">API Cache (seconds)</label>
                        <input type="number" class="form-control @error('api_cache') is-invalid @enderror" id="api_cache" name="api_cache" value="{{ old('api_cache', 0) }}" min="0" max="999">
                        <small class="text-muted">3 digit numerical, default 0</small>
                        @error('api_cache')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Permissions Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">Permissions</h6>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label">POS Types Allowed</label>
                        <div class="d-flex gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="pos_b2b" name="pos_types_allowed[]" value="B2B" {{ is_array(old('pos_types_allowed')) && in_array('B2B', old('pos_types_allowed')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="pos_b2b">B2B</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="pos_b2c" name="pos_types_allowed[]" value="B2C" {{ is_array(old('pos_types_allowed')) && in_array('B2C', old('pos_types_allowed')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="pos_b2c">B2C</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="pos_meta" name="pos_types_allowed[]" value="META" {{ is_array(old('pos_types_allowed')) && in_array('META', old('pos_types_allowed')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="pos_meta">META</label>
                            </div>
                        </div>
                        @error('pos_types_allowed')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Distribution Allowed</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="distribution_allowed" name="distribution_allowed" value="1" {{ old('distribution_allowed', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="distribution_allowed">
                                <span class="switch-label">Enabled</span>
                            </label>
                        </div>
                        @error('distribution_allowed')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <label class="form-label">Operations Allowed</label>
                        <div class="d-flex gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="op_search" name="operations_allowed[]" value="Search" {{ is_array(old('operations_allowed')) && in_array('Search', old('operations_allowed')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="op_search">Search</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="op_pricing" name="operations_allowed[]" value="Pricing" {{ is_array(old('operations_allowed')) && in_array('Pricing', old('operations_allowed')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="op_pricing">Pricing</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="op_booking" name="operations_allowed[]" value="Booking" {{ is_array(old('operations_allowed')) && in_array('Booking', old('operations_allowed')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="op_booking">Booking</label>
                            </div>
                        </div>
                        @error('operations_allowed')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- AAA Configuration (Conditional) -->
                <div class="row mb-4" id="aaa_section">
                    <div class="col-12">
                        <h6 class="mb-3">AAA Configuration</h6>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="aaa_enabled" name="aaa_enabled" value="1" {{ old('aaa_enabled') ? 'checked' : '' }}>
                            <label class="form-check-label" for="aaa_enabled">Enable AAA</label>
                        </div>
                        <small class="text-muted">For Sabre only (for now)</small>
                        @error('aaa_enabled')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3 aaa-field">
                        <label for="aaa_pcc" class="form-label">AAA PCC</label>
                        <input type="text" class="form-control @error('aaa_pcc') is-invalid @enderror" id="aaa_pcc" name="aaa_pcc" value="{{ old('aaa_pcc') }}" maxlength="4">
                        <small class="text-muted">4 characters max</small>
                        @error('aaa_pcc')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-4 mb-3 aaa-field">
                        <label class="form-label">AAA Operations</label>
                        <div class="d-flex gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="aaa_op_search" name="aaa_operations[]" value="Search" {{ is_array(old('aaa_operations')) && in_array('Search', old('aaa_operations')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="aaa_op_search">Search</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="aaa_op_pricing" name="aaa_operations[]" value="Pricing" {{ is_array(old('aaa_operations')) && in_array('Pricing', old('aaa_operations')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="aaa_op_pricing">Pricing</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="aaa_op_booking" name="aaa_operations[]" value="Booking" {{ is_array(old('aaa_operations')) && in_array('Booking', old('aaa_operations')) ? 'checked' : '' }}>
                                <label class="form-check-label" for="aaa_op_booking">Booking</label>
                            </div>
                        </div>
                        @error('aaa_operations')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Booking Remarks -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">Booking Remarks</h6>
                    </div>
                    
                    <div class="col-12 mb-3">
                        <label for="booking_remarks_1" class="form-label">Remark Line 1</label>
                        <input type="text" class="form-control @error('booking_remarks_1') is-invalid @enderror" id="booking_remarks_1" name="booking_remarks[]" value="{{ old('booking_remarks.0') }}" maxlength="70">
                        <small class="text-muted">70 characters max</small>
                        @error('booking_remarks.0')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                </div>
                
                <!-- Queue Configuration -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">Queue Configuration</h6>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="queue_default" class="form-label">Default</label>
                        <input type="text" class="form-control @error('queue_default') is-invalid @enderror" id="queue_default" name="queue_default" value="{{ old('queue_default') }}" maxlength="10" pattern="[a-zA-Z0-9]+" title="Alphanumeric characters only">
                        <small class="text-muted">Max 10 alphanumeric characters</small>
                        @error('queue_default')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="queue_hold" class="form-label">Hold</label>
                        <input type="text" class="form-control @error('queue_hold') is-invalid @enderror" id="queue_hold" name="queue_hold" value="{{ old('queue_hold') }}" maxlength="10" pattern="[a-zA-Z0-9]+">
                        @error('queue_hold')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="queue_cc_payment" class="form-label">CC Payment</label>
                        <input type="text" class="form-control @error('queue_cc_payment') is-invalid @enderror" id="queue_cc_payment" name="queue_cc_payment" value="{{ old('queue_cc_payment') }}" maxlength="10" pattern="[a-zA-Z0-9]+">
                        @error('queue_cc_payment')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="queue_cheque" class="form-label">Cheque</label>
                        <input type="text" class="form-control @error('queue_cheque') is-invalid @enderror" id="queue_cheque" name="queue_cheque" value="{{ old('queue_cheque') }}" maxlength="10" pattern="[a-zA-Z0-9]+">
                        @error('queue_cheque')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="queue_pay_later" class="form-label">Pay later</label>
                        <input type="text" class="form-control @error('queue_pay_later') is-invalid @enderror" id="queue_pay_later" name="queue_pay_later" value="{{ old('queue_pay_later') }}" maxlength="10" pattern="[a-zA-Z0-9]+">
                        @error('queue_pay_later')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="queue_merchant" class="form-label">Merchant</label>
                        <input type="text" class="form-control @error('queue_merchant') is-invalid @enderror" id="queue_merchant" name="queue_merchant" value="{{ old('queue_merchant') }}" maxlength="10" pattern="[a-zA-Z0-9]+">
                        @error('queue_merchant')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="queue_cancel" class="form-label">Cancel</label>
                        <input type="text" class="form-control @error('queue_cancel') is-invalid @enderror" id="queue_cancel" name="queue_cancel" value="{{ old('queue_cancel') }}" maxlength="10" pattern="[a-zA-Z0-9]+">
                        @error('queue_cancel')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <label for="queue_ticketing" class="form-label">Ticketing</label>
                        <input type="text" class="form-control @error('queue_ticketing') is-invalid @enderror" id="queue_ticketing" name="queue_ticketing" value="{{ old('queue_ticketing') }}" maxlength="10" pattern="[a-zA-Z0-9]+">
                        @error('queue_ticketing')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- Environment Configuration -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">Environment Configuration</h6>
                    </div>
                    
                    <div class="col-12 mb-3">
                        <label class="form-label">Enabled Environments</label>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="env_prod" name="environments[]" value="PROD" {{ is_array(old('environments')) && in_array('PROD', old('environments')) ? 'checked' : '' }}>
                            <label class="form-check-label" for="env_prod">PROD</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="env_cert" name="environments[]" value="CERT" {{ is_array(old('environments')) && in_array('CERT', old('environments')) ? 'checked' : '' }}>
                            <label class="form-check-label" for="env_cert">CERT</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="env_test" name="environments[]" value="TEST" {{ is_array(old('environments')) && in_array('TEST', old('environments')) ? 'checked' : '' }}>
                            <label class="form-check-label" for="env_test">TEST</label>
                        </div>
                        @error('environments')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
                
                <!-- API Connection Details (Conditional per environment) -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3">API Connection Details</h6>
                    </div>
                    
                    <!-- PROD Environment -->
                    <div class="col-12 mb-4 api-env-section" id="api_prod_section">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">PROD Environment</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label for="prod_api_endpoint" class="form-label">API End-Point</label>
                                        <input type="text" class="form-control api-field @error('prod_api_endpoint') is-invalid @enderror" id="prod_api_endpoint" name="prod_api_endpoint" value="{{ old('prod_api_endpoint') }}">
                                        @error('prod_api_endpoint')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="prod_username" class="form-label">User Name</label>
                                        <input type="text" class="form-control api-field @error('prod_username') is-invalid @enderror" id="prod_username" name="prod_username" value="{{ old('prod_username') }}">
                                        @error('prod_username')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="prod_password" class="form-label">Password</label>
                                        <input type="password" class="form-control api-field @error('prod_password') is-invalid @enderror" id="prod_password" name="prod_password" value="{{ old('prod_password') }}">
                                        @error('prod_password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="prod_domain" class="form-label">Domain</label>
                                        <input type="text" class="form-control api-field @error('prod_domain') is-invalid @enderror" id="prod_domain" name="prod_domain" value="{{ old('prod_domain') }}">
                                        @error('prod_domain')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="prod_token" class="form-label">Token</label>
                                        <input type="text" class="form-control api-field @error('prod_token') is-invalid @enderror" id="prod_token" name="prod_token" value="{{ old('prod_token') }}">
                                        <small class="text-muted">Only for REST API</small>
                                        @error('prod_token')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="prod_max_itins" class="form-label">Max Itins</label>
                                        <input type="number" class="form-control api-field @error('prod_max_itins') is-invalid @enderror" id="prod_max_itins" name="prod_max_itins" value="{{ old('prod_max_itins') }}">
                                        @error('prod_max_itins')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="prod_allowed_itins" class="form-label">Allowed Itins</label>
                                        <input type="text" class="form-control api-field @error('prod_allowed_itins') is-invalid @enderror" id="prod_allowed_itins" name="prod_allowed_itins" value="{{ old('prod_allowed_itins') }}">
                                        @error('prod_allowed_itins')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Fare Type</label>
                                        <div class="d-flex flex-wrap gap-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="fare_pub" name="fare_type[]" value="PUB" {{ is_array(old('fare_type')) && in_array('PUB', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="fare_pub">PUB</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="fare_pvt" name="fare_type[]" value="PVT" {{ is_array(old('fare_type')) && in_array('PVT', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="fare_pvt">PVT</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="fare_net" name="fare_type[]" value="NET" {{ is_array(old('fare_type')) && in_array('NET', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="fare_net">NET</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="fare_itx" name="fare_type[]" value="ITX" {{ is_array(old('fare_type')) && in_array('ITX', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="fare_itx">ITX</label>
                                            </div>
                                        </div>
                                        @error('fare_type')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- CERT Environment -->
                    <div class="col-12 mb-4 api-env-section" id="api_cert_section">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">CERT Environment</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label for="cert_api_endpoint" class="form-label">API End-Point</label>
                                        <input type="text" class="form-control api-field @error('cert_api_endpoint') is-invalid @enderror" id="cert_api_endpoint" name="cert_api_endpoint" value="{{ old('cert_api_endpoint') }}">
                                        @error('cert_api_endpoint')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="cert_username" class="form-label">User Name</label>
                                        <input type="text" class="form-control api-field @error('cert_username') is-invalid @enderror" id="cert_username" name="cert_username" value="{{ old('cert_username') }}">
                                        @error('cert_username')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="cert_password" class="form-label">Password</label>
                                        <input type="password" class="form-control api-field @error('cert_password') is-invalid @enderror" id="cert_password" name="cert_password" value="{{ old('cert_password') }}">
                                        @error('cert_password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="cert_domain" class="form-label">Domain</label>
                                        <input type="text" class="form-control api-field @error('cert_domain') is-invalid @enderror" id="cert_domain" name="cert_domain" value="{{ old('cert_domain') }}">
                                        @error('cert_domain')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="cert_token" class="form-label">Token</label>
                                        <input type="text" class="form-control api-field @error('cert_token') is-invalid @enderror" id="cert_token" name="cert_token" value="{{ old('cert_token') }}">
                                        <small class="text-muted">Only for REST API</small>
                                        @error('cert_token')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="cert_max_itins" class="form-label">Max Itins</label>
                                        <input type="number" class="form-control api-field @error('cert_max_itins') is-invalid @enderror" id="cert_max_itins" name="cert_max_itins" value="{{ old('cert_max_itins') }}">
                                        @error('cert_max_itins')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="cert_allowed_itins" class="form-label">Allowed Itins</label>
                                        <input type="text" class="form-control api-field @error('cert_allowed_itins') is-invalid @enderror" id="cert_allowed_itins" name="cert_allowed_itins" value="{{ old('cert_allowed_itins') }}">
                                        @error('cert_allowed_itins')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Fare Type</label>
                                        <div class="d-flex flex-wrap gap-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="cert_fare_pub" name="fare_type[]" value="PUB" {{ is_array(old('fare_type')) && in_array('PUB', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="cert_fare_pub">PUB</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="cert_fare_pvt" name="fare_type[]" value="PVT" {{ is_array(old('fare_type')) && in_array('PVT', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="cert_fare_pvt">PVT</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="cert_fare_net" name="fare_type[]" value="NET" {{ is_array(old('fare_type')) && in_array('NET', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="cert_fare_net">NET</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="cert_fare_itx" name="fare_type[]" value="ITX" {{ is_array(old('fare_type')) && in_array('ITX', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="cert_fare_itx">ITX</label>
                                            </div>
                                        </div>
                                        @error('fare_type')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- TEST Environment -->
                    <div class="col-12 mb-4 api-env-section" id="api_test_section">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">TEST Environment</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label for="test_api_endpoint" class="form-label">API End-Point</label>
                                        <input type="text" class="form-control api-field @error('test_api_endpoint') is-invalid @enderror" id="test_api_endpoint" name="test_api_endpoint" value="{{ old('test_api_endpoint') }}">
                                        @error('test_api_endpoint')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="test_username" class="form-label">User Name</label>
                                        <input type="text" class="form-control api-field @error('test_username') is-invalid @enderror" id="test_username" name="test_username" value="{{ old('test_username') }}">
                                        @error('test_username')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="test_password" class="form-label">Password</label>
                                        <input type="password" class="form-control api-field @error('test_password') is-invalid @enderror" id="test_password" name="test_password" value="{{ old('test_password') }}">
                                        @error('test_password')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="test_domain" class="form-label">Domain</label>
                                        <input type="text" class="form-control api-field @error('test_domain') is-invalid @enderror" id="test_domain" name="test_domain" value="{{ old('test_domain') }}">
                                        @error('test_domain')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="test_token" class="form-label">Token</label>
                                        <input type="text" class="form-control api-field @error('test_token') is-invalid @enderror" id="test_token" name="test_token" value="{{ old('test_token') }}">
                                        <small class="text-muted">Only for REST API</small>
                                        @error('test_token')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="test_max_itins" class="form-label">Max Itins</label>
                                        <input type="number" class="form-control api-field @error('test_max_itins') is-invalid @enderror" id="test_max_itins" name="test_max_itins" value="{{ old('test_max_itins') }}">
                                        @error('test_max_itins')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    
                                    <div class="col-md-3 mb-3">
                                        <label for="test_allowed_itins" class="form-label">Allowed Itins</label>
                                        <input type="text" class="form-control api-field @error('test_allowed_itins') is-invalid @enderror" id="test_allowed_itins" name="test_allowed_itins" value="{{ old('test_allowed_itins') }}">
                                        @error('test_allowed_itins')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Fare Type</label>
                                        <div class="d-flex flex-wrap gap-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="test_fare_pub" name="fare_type[]" value="PUB" {{ is_array(old('fare_type')) && in_array('PUB', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="test_fare_pub">PUB</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="test_fare_pvt" name="fare_type[]" value="PVT" {{ is_array(old('fare_type')) && in_array('PVT', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="test_fare_pvt">PVT</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="test_fare_net" name="fare_type[]" value="NET" {{ is_array(old('fare_type')) && in_array('NET', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="test_fare_net">NET</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="test_fare_itx" name="fare_type[]" value="ITX" {{ is_array(old('fare_type')) && in_array('ITX', old('fare_type')) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="test_fare_itx">ITX</label>
                                            </div>
                                        </div>
                                        @error('fare_type')
                                            <div class="invalid-feedback d-block">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Status -->
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="status" name="status" {{ old('status', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="status">Enabled</label>
                        </div>
                        @error('status')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="row mt-4">
                    <div class="col-lg-2">
                        <a href="{{ route('backend.content_sources.index') }}" class="btn btn-secondary">Back</a>
                    </div>
                    <div class="col-lg-9"></div>
                    <div class="col-lg-1">
                        <button type="submit" class="btn btn-primary">Create</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@section('page-script')
<script>
    $(document).ready(function() {
        // Initialize Tagify for currencies
        initializeCurrencyTagify();

        // Initialize Select2 for countries
        initializeCountrySelect2();

        // Initialize Select2 for timezones
        initializeTimezoneSelect2();

        // Initialize Select2 for currencies
        initializeCurrencySelect2();

        // Initialize Select2 for other fields
        initializeOtherSelect2();
    });

    // Initialize Tagify for currencies
    function initializeCurrencyTagify() {
        const currencyTextarea = document.querySelector('#allowed_currencies');
        if (currencyTextarea) {
            const currencyTagify = new Tagify(currencyTextarea, {
                enforceWhitelist: true,
                whitelist: [
                    {value: 'USD', id: 'USD'},
                    {value: 'EUR', id: 'EUR'},
                    {value: 'GBP', id: 'GBP'},
                    {value: 'CAD', id: 'CAD'},
                    {value: 'AUD', id: 'AUD'},
                    {value: 'JPY', id: 'JPY'},
                    {value: 'CHF', id: 'CHF'},
                    {value: 'CNY', id: 'CNY'},
                    {value: 'INR', id: 'INR'},
                    {value: 'SGD', id: 'SGD'},
                    {value: 'HKD', id: 'HKD'},
                    {value: 'NZD', id: 'NZD'},
                    {value: 'SEK', id: 'SEK'},
                    {value: 'NOK', id: 'NOK'},
                    {value: 'DKK', id: 'DKK'},
                    {value: 'PLN', id: 'PLN'},
                    {value: 'CZK', id: 'CZK'},
                    {value: 'HUF', id: 'HUF'},
                    {value: 'RUB', id: 'RUB'},
                    {value: 'BRL', id: 'BRL'},
                    {value: 'MXN', id: 'MXN'},
                    {value: 'ZAR', id: 'ZAR'},
                    {value: 'KRW', id: 'KRW'},
                    {value: 'THB', id: 'THB'},
                    {value: 'MYR', id: 'MYR'},
                    {value: 'PHP', id: 'PHP'},
                    {value: 'IDR', id: 'IDR'},
                    {value: 'VND', id: 'VND'},
                    {value: 'AED', id: 'AED'},
                    {value: 'SAR', id: 'SAR'},
                    {value: 'QAR', id: 'QAR'},
                    {value: 'KWD', id: 'KWD'},
                    {value: 'BHD', id: 'BHD'},
                    {value: 'OMR', id: 'OMR'},
                    {value: 'JOD', id: 'JOD'},
                    {value: 'LBP', id: 'LBP'},
                    {value: 'EGP', id: 'EGP'},
                    {value: 'TRY', id: 'TRY'},
                    {value: 'ILS', id: 'ILS'},
                    {value: 'RON', id: 'RON'},
                    {value: 'BGN', id: 'BGN'},
                    {value: 'HRK', id: 'HRK'},
                    {value: 'RSD', id: 'RSD'},
                    {value: 'UAH', id: 'UAH'},
                    {value: 'KZT', id: 'KZT'},
                    {value: 'UZS', id: 'UZS'},
                    {value: 'AMD', id: 'AMD'},
                    {value: 'GEL', id: 'GEL'},
                    {value: 'AZN', id: 'AZN'},
                    {value: 'BYN', id: 'BYN'},
                    {value: 'MDL', id: 'MDL'}
                ],
                dropdown: {
                    maxItems: 20,
                    classname: "custom-look",
                    enabled: 1,
                    closeOnSelect: false
                },
                maxTags: 10
            });
        }
    }
    // Initialize Select2 for countries
    function initializeCountrySelect2() {
        var countries = {!! $country_list_json !!};

        // Transform the data to Select2 format
        var countryData = countries.map(function(country) {
            return {
                id: country.code,
                text: country.name + ' (' + country.code + ')'
            };
        });

        $('.country-select2').select2({
            placeholder: "Select country",
            allowClear: true,
            data: countryData,
            width: '100%'
        });
    }

    // Initialize Select2 for timezones
    function initializeTimezoneSelect2() {
        $('.timezone-select2').select2({
            placeholder: "Select timezone",
            allowClear: true,
            data: [
                {id: 'UTC', text: 'UTC - Coordinated Universal Time'},
                {id: 'America/New_York', text: 'America/New_York - Eastern Time'},
                {id: 'America/Chicago', text: 'America/Chicago - Central Time'},
                {id: 'America/Denver', text: 'America/Denver - Mountain Time'},
                {id: 'America/Los_Angeles', text: 'America/Los_Angeles - Pacific Time'},
                {id: 'Europe/London', text: 'Europe/London - Greenwich Mean Time'},
                {id: 'Europe/Paris', text: 'Europe/Paris - Central European Time'},
                {id: 'Europe/Berlin', text: 'Europe/Berlin - Central European Time'},
                {id: 'Europe/Rome', text: 'Europe/Rome - Central European Time'},
                {id: 'Europe/Madrid', text: 'Europe/Madrid - Central European Time'},
                {id: 'Europe/Amsterdam', text: 'Europe/Amsterdam - Central European Time'},
                {id: 'Europe/Brussels', text: 'Europe/Brussels - Central European Time'},
                {id: 'Europe/Vienna', text: 'Europe/Vienna - Central European Time'},
                {id: 'Europe/Zurich', text: 'Europe/Zurich - Central European Time'},
                {id: 'Europe/Prague', text: 'Europe/Prague - Central European Time'},
                {id: 'Europe/Warsaw', text: 'Europe/Warsaw - Central European Time'},
                {id: 'Europe/Budapest', text: 'Europe/Budapest - Central European Time'},
                {id: 'Europe/Stockholm', text: 'Europe/Stockholm - Central European Time'},
                {id: 'Europe/Oslo', text: 'Europe/Oslo - Central European Time'},
                {id: 'Europe/Copenhagen', text: 'Europe/Copenhagen - Central European Time'},
                {id: 'Europe/Helsinki', text: 'Europe/Helsinki - Eastern European Time'},
                {id: 'Europe/Athens', text: 'Europe/Athens - Eastern European Time'},
                {id: 'Europe/Istanbul', text: 'Europe/Istanbul - Turkey Time'},
                {id: 'Europe/Moscow', text: 'Europe/Moscow - Moscow Time'},
                {id: 'Asia/Tokyo', text: 'Asia/Tokyo - Japan Standard Time'},
                {id: 'Asia/Shanghai', text: 'Asia/Shanghai - China Standard Time'},
                {id: 'Asia/Hong_Kong', text: 'Asia/Hong_Kong - Hong Kong Time'},
                {id: 'Asia/Singapore', text: 'Asia/Singapore - Singapore Time'},
                {id: 'Asia/Bangkok', text: 'Asia/Bangkok - Indochina Time'},
                {id: 'Asia/Jakarta', text: 'Asia/Jakarta - Western Indonesian Time'},
                {id: 'Asia/Manila', text: 'Asia/Manila - Philippine Time'},
                {id: 'Asia/Seoul', text: 'Asia/Seoul - Korea Standard Time'},
                {id: 'Asia/Taipei', text: 'Asia/Taipei - Taipei Time'},
                {id: 'Asia/Kuala_Lumpur', text: 'Asia/Kuala_Lumpur - Malaysia Time'},
                {id: 'Asia/Ho_Chi_Minh', text: 'Asia/Ho_Chi_Minh - Indochina Time'},
                {id: 'Asia/Kolkata', text: 'Asia/Kolkata - India Standard Time'},
                {id: 'Asia/Dubai', text: 'Asia/Dubai - Gulf Standard Time'},
                {id: 'Asia/Riyadh', text: 'Asia/Riyadh - Arabia Standard Time'},
                {id: 'Asia/Tehran', text: 'Asia/Tehran - Iran Standard Time'},
                {id: 'Asia/Baghdad', text: 'Asia/Baghdad - Arabia Standard Time'},
                {id: 'Asia/Jerusalem', text: 'Asia/Jerusalem - Israel Standard Time'},
                {id: 'Australia/Sydney', text: 'Australia/Sydney - Australian Eastern Time'},
                {id: 'Australia/Melbourne', text: 'Australia/Melbourne - Australian Eastern Time'},
                {id: 'Australia/Brisbane', text: 'Australia/Brisbane - Australian Eastern Time'},
                {id: 'Australia/Perth', text: 'Australia/Perth - Australian Western Time'},
                {id: 'Australia/Adelaide', text: 'Australia/Adelaide - Australian Central Time'},
                {id: 'Pacific/Auckland', text: 'Pacific/Auckland - New Zealand Time'},
                {id: 'Pacific/Fiji', text: 'Pacific/Fiji - Fiji Time'},
                {id: 'Pacific/Honolulu', text: 'Pacific/Honolulu - Hawaii Time'},
                {id: 'America/Toronto', text: 'America/Toronto - Eastern Time'},
                {id: 'America/Vancouver', text: 'America/Vancouver - Pacific Time'},
                {id: 'America/Montreal', text: 'America/Montreal - Eastern Time'},
                {id: 'America/Sao_Paulo', text: 'America/Sao_Paulo - Brasilia Time'},
                {id: 'America/Mexico_City', text: 'America/Mexico_City - Central Time'},
                {id: 'America/Buenos_Aires', text: 'America/Buenos_Aires - Argentina Time'},
                {id: 'America/Lima', text: 'America/Lima - Peru Time'},
                {id: 'America/Bogota', text: 'America/Bogota - Colombia Time'},
                {id: 'America/Santiago', text: 'America/Santiago - Chile Time'},
                {id: 'America/Caracas', text: 'America/Caracas - Venezuela Time'},
                {id: 'Africa/Cairo', text: 'Africa/Cairo - Eastern European Time'},
                {id: 'Africa/Lagos', text: 'Africa/Lagos - West Africa Time'},
                {id: 'Africa/Johannesburg', text: 'Africa/Johannesburg - South Africa Time'},
                {id: 'Africa/Nairobi', text: 'Africa/Nairobi - East Africa Time'},
                {id: 'Africa/Casablanca', text: 'Africa/Casablanca - Western European Time'},
                {id: 'Africa/Algiers', text: 'Africa/Algiers - Central European Time'}
            ]
        });
    }

    // Initialize Select2 for currencies
    function initializeCurrencySelect2() {
        $('.currency-select2').select2({
            placeholder: "Select currency",
            allowClear: true,
            data: [
                {id: 'USD', text: 'USD - US Dollar'},
                {id: 'EUR', text: 'EUR - Euro'},
                {id: 'GBP', text: 'GBP - British Pound'},
                {id: 'CAD', text: 'CAD - Canadian Dollar'},
                {id: 'AUD', text: 'AUD - Australian Dollar'},
                {id: 'JPY', text: 'JPY - Japanese Yen'},
                {id: 'CHF', text: 'CHF - Swiss Franc'},
                {id: 'CNY', text: 'CNY - Chinese Yuan'},
                {id: 'INR', text: 'INR - Indian Rupee'},
                {id: 'SGD', text: 'SGD - Singapore Dollar'},
                {id: 'HKD', text: 'HKD - Hong Kong Dollar'},
                {id: 'NZD', text: 'NZD - New Zealand Dollar'},
                {id: 'SEK', text: 'SEK - Swedish Krona'},
                {id: 'NOK', text: 'NOK - Norwegian Krone'},
                {id: 'DKK', text: 'DKK - Danish Krone'},
                {id: 'PLN', text: 'PLN - Polish Zloty'},
                {id: 'CZK', text: 'CZK - Czech Koruna'},
                {id: 'HUF', text: 'HUF - Hungarian Forint'},
                {id: 'RUB', text: 'RUB - Russian Ruble'},
                {id: 'BRL', text: 'BRL - Brazilian Real'},
                {id: 'MXN', text: 'MXN - Mexican Peso'},
                {id: 'ZAR', text: 'ZAR - South African Rand'},
                {id: 'KRW', text: 'KRW - South Korean Won'},
                {id: 'THB', text: 'THB - Thai Baht'},
                {id: 'MYR', text: 'MYR - Malaysian Ringgit'},
                {id: 'PHP', text: 'PHP - Philippine Peso'},
                {id: 'IDR', text: 'IDR - Indonesian Rupiah'},
                {id: 'VND', text: 'VND - Vietnamese Dong'},
                {id: 'AED', text: 'AED - UAE Dirham'},
                {id: 'SAR', text: 'SAR - Saudi Riyal'},
                {id: 'QAR', text: 'QAR - Qatari Riyal'},
                {id: 'KWD', text: 'KWD - Kuwaiti Dinar'},
                {id: 'BHD', text: 'BHD - Bahraini Dinar'},
                {id: 'OMR', text: 'OMR - Omani Rial'},
                {id: 'JOD', text: 'JOD - Jordanian Dinar'},
                {id: 'LBP', text: 'LBP - Lebanese Pound'},
                {id: 'EGP', text: 'EGP - Egyptian Pound'},
                {id: 'TRY', text: 'TRY - Turkish Lira'},
                {id: 'ILS', text: 'ILS - Israeli Shekel'},
                {id: 'RON', text: 'RON - Romanian Leu'},
                {id: 'BGN', text: 'BGN - Bulgarian Lev'},
                {id: 'HRK', text: 'HRK - Croatian Kuna'},
                {id: 'RSD', text: 'RSD - Serbian Dinar'},
                {id: 'UAH', text: 'UAH - Ukrainian Hryvnia'},
                {id: 'KZT', text: 'KZT - Kazakhstani Tenge'},
                {id: 'UZS', text: 'UZS - Uzbekistani Som'},
                {id: 'AMD', text: 'AMD - Armenian Dram'},
                {id: 'GEL', text: 'GEL - Georgian Lari'},
                {id: 'AZN', text: 'AZN - Azerbaijani Manat'},
                {id: 'BYN', text: 'BYN - Belarusian Ruble'},
                {id: 'MDL', text: 'MDL - Moldovan Leu'}
            ]
        });
    }

    // Initialize Select2 for Product, GDS, and API Method
    function initializeOtherSelect2() {
        // Product Select2 - using server-side rendered options
        $('.product-select2').select2({
            placeholder: "Select product",
            allowClear: true,
            width: '100%'
        });

        // GDS Select2
        $('.gds-select2').select2({
            placeholder: "Select GDS",
            allowClear: true,
            width: '100%'
        });

        // API Method Select2
        $('.api-method-select2').select2({
            placeholder: "Select API method",
            allowClear: true,
            width: '100%'
        });
    }

    // Toggle AAA fields based on GDS selection and AAA checkbox
    function toggleAaaFields() {
        const isSabre = $('#gds').val() === '1S';
        const isAaaEnabled = $('#aaa_enabled').is(':checked');

        $('#aaa_section').toggle(isSabre);
        $('.aaa-field').toggle(isSabre && isAaaEnabled);
    }

    $('#gds, #aaa_enabled').change(toggleAaaFields);
    toggleAaaFields(); // Initialize on page load

    // Handle distribution toggle switch label
    function updateDistributionLabel() {
        const toggle = $('#distribution_allowed');
        const label = toggle.siblings('label').find('.switch-label');
        if (toggle.is(':checked')) {
            label.text('Enabled');
            label.removeClass('text-muted').addClass('text-success');
        } else {
            label.text('Disabled');
            label.removeClass('text-success').addClass('text-muted');
        }
    }

    $('#distribution_allowed').change(updateDistributionLabel);
    updateDistributionLabel(); // Initialize on page load

    // Update PCC/Office ID label based on GDS selection
    $('#gds').change(function() {
        const gds = $(this).val();
        if (gds === '1S') {
            $('#pcc_office_label').text('PCC');
        } else if (gds === '1A') {
            $('#pcc_office_label').text('Office ID');
        }
    }).trigger('change');

    // Toggle API environment sections based on checkbox selection
    function toggleApiEnvSections() {
        $('#env_prod').is(':checked') ?
            $('#api_prod_section').show().find('.api-field').prop('disabled', false) :
            $('#api_prod_section').hide().find('.api-field').prop('disabled', true);

        $('#env_cert').is(':checked') ?
            $('#api_cert_section').show().find('.api-field').prop('disabled', false) :
            $('#api_cert_section').hide().find('.api-field').prop('disabled', true);

        $('#env_test').is(':checked') ?
            $('#api_test_section').show().find('.api-field').prop('disabled', false) :
            $('#api_test_section').hide().find('.api-field').prop('disabled', true);
    }

    $('input[name="environments[]"]').change(toggleApiEnvSections);
    toggleApiEnvSections(); // Initialize on page load

    // Form validation
    $('#content-source-form').validate({
        rules: {
            name: {
                required: true,
                minlength: 3
            },
            identifier: {
                required: true,
                minlength: 1,
                maxlength: 15,
                noSpaces: true
            },
            gds: {
                required: true
            },
            product: {
                required: true
            }
        },
        messages: {
            name: {
                required: "Please enter a name for the content source",
                minlength: "Name must be at least 3 characters long"
            },
            identifier: {
                required: "Please enter an identifier",
                minlength: "Identifier must be at least 1 character long",
                maxlength: "Identifier cannot be longer than 15 characters",
                noSpaces: "Identifier cannot contain spaces"
            },
            gds: {
                required: "Please select a GDS"
            },
            product: {
                required: "Please select a product"
            }
        },
        errorElement: 'span',
        errorPlacement: function (error, element) {
            error.addClass('invalid-feedback');
            element.closest('.form-group').append(error);
        },
        highlight: function (element, errorClass, validClass) {
            $(element).addClass('is-invalid');
        },
        unhighlight: function (element, errorClass, validClass) {
            $(element).removeClass('is-invalid');
        }
    });

    // Custom validation method for no spaces
    $.validator.addMethod("noSpaces", function(value, element) {
        return this.optional(element) || !/\s/.test(value);
    }, "No spaces allowed");
</script>
@endsection