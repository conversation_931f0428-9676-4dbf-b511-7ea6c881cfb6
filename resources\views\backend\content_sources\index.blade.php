@extends('layouts.layoutMaster')

@section('title', 'Content Sources')

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/flatpickr/flatpickr.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/flatpickr/flatpickr.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
@endsection

@section('page-script')
    <script src="{{ asset('assets/js/contentsources/index.js') }}"></script>
@endsection

@section('content')
    <div class="card header">
        <div class="card-body py-2">
            <div class="col-md-4 d-flex">
                <h5 class="mx-2 mt-2 text-white">Content Sources</h5>
                <div class="mx-2 mt-1">
                    <a href="{{ route('backend.content_sources.create') }}" class="btn btn-primary d-flex align-items-center">
                        <i class="ti ti-plus me-2" style="margin-top: -2px;"></i>
                        New
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-body" data-masking-url="{{ route('backend.masking_master.index') }}">
            @if (session('success'))
                <div class="alert alert-success" id="alertMessage">
                    {{ session('success') }}
                </div>
            @endif

            @if (session('error'))
                <div class="alert alert-danger" id="alertMessage">
                    {{ session('error') }}
                </div>
            @endif

            <div class="table-responsive">
                <table class="table AirlineMasking" id="AirlineMasking" style="width:100%">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Table Number</th>
                            <th>Original Carriers</th>
                            <th>Masked Name</th>
                            <th>Masked Code</th>
                            <th>Default Name</th>
                            <th>Default Code</th>
                            <th>Created At</th>
                            <th>Updated At</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
@endsection