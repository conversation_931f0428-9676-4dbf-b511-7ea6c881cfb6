<?php

namespace App\Http\Controllers\backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class ContentSourcesController extends Controller
{
  public function index(){
    return view('backend.content_sources.index');
  }

  public function create(){
    $country_list = file_get_contents(public_path() . "/resources/CountryCodes.json");
    $received_from = config('custom-app.content_sources_received_from');
    $products = config('custom-app.content_sources_product');
    return view('backend.content_sources.create', ['country_list_json' => $country_list, 'received_from' => $received_from,'products'=>$products]);
  }

  public function store(Request $request){
    echo '<pre>';
    print_r($request->all());
    echo '<prE>';
    return;
  }
}
