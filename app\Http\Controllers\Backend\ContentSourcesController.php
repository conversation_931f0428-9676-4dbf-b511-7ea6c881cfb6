<?php

namespace App\Http\Controllers\backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class ContentSourcesController extends Controller
{
  public function index(){
    return view('backend.content_sources.index');
  }

  public function create(){
    return view('backend.content_sources.create');
  }

  public function store(Request $request){
    echo '<pre>';
    print_r($request->all());
    echo '<prE>';
    return;
  }
}
