

<?php $__env->startSection('title', 'Edit Airline Masking'); ?>

<?php $__env->startSection('vendor-style'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/5.1.0/css/bootstrap.min.css">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/tagify/tagify.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/typeahead-js/typeahead.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('vendor-script'); ?>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js')); ?>"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"
        integrity="sha256-xLD7nhI62fcsEZK2/v8LsBcb4lG7dgULkuXoXB/j91c=" crossorigin="anonymous"></script>

    <!-- Flat Picker -->
    <script src="<?php echo e(asset('assets/vendor/libs/moment/moment.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/flatpickr/flatpickr.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/sweetalert2/sweetalert2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/tagify/tagify.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/forms-typeahead.js')); ?>"></script>
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

        </div>
    <?php endif; ?>
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-body" style="padding:20px 35px;">
            <div class="row mb-4">
                <div class="col-11">
                    <h5>Edit Airline Masking</h5>
                </div>
            </div>

            <form method="POST" action="<?php echo e(route('backend.airline_masking.update', $airlineMasking->id)); ?>" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                
                <div id="parent-container">
                    <div class="card row p-3" id="repeatable-section-0">
                        <div class="row">
                            <div class="col-lg-2">
                                <label>Portal Mode</label>
                                <select name="portal_mode-0" id="portal_mode-0" class="form-control required select2" required>
                                    <option value="PORTAL" <?php echo e($airlineMasking->portal_mode === 'PORTAL' ? 'selected' : ''); ?>>Portal</option>
                                    <option value="META" <?php echo e($airlineMasking->portal_mode === 'META' ? 'selected' : ''); ?>>Meta</option>
                                </select>
                            </div>
                            <div class="col-lg-2">
                                <label>POS</label>
                                <select name="pos-0" id="pos-0" class="form-control required select2" required>
                                    <option value="US" <?php echo e(($maskingData['POS'] ?? 'US') === 'US' ? 'selected' : ''); ?>>US</option>
                                    <option value="CA" <?php echo e(($maskingData['POS'] ?? 'US') === 'CA' ? 'selected' : ''); ?>>CA</option>
                                </select>
                            </div>

                            <div class="col-lg-2">
                                <label>Portal</label>
                                <select id="portal-0" class="form-select select2" required name="portal-0">
                                    <?php $__currentLoopData = config('custom-app.portals'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $urls): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php $__currentLoopData = $urls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php $cleanUrl = str_replace('https://', '', $url); ?>
                                            <option value="<?php echo e($cleanUrl); ?>" <?php echo e($cleanUrl === $airlineMasking->portal ? 'selected' : ''); ?>>
                                                <?php echo e($cleanUrl); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>

                             <div class="col-lg-2">
                                <label>Content Sources</label>
                                <select id="content_sources-0" class="form-select select2" name="content_sources-0[]" multiple>
                                    <?php $__currentLoopData = $formatted_content_sources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" 
                                            <?php if(in_array($key, $selected_content_sources)): ?> selected <?php endif; ?>>
                                            <?php echo e($value); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                             </div>

                            <div class="col-lg-2">
                                <label>Fare Types</label>
                                <select id="fareType-0" class="form-select select2" required name="fare_type-0[]" multiple>
                                    <?php
                                        $fareTypes = explode(',', $airlineMasking->fare_types);
                                    ?>
                                    <option value="Published" <?php echo e(in_array('Published', $fareTypes) ? 'selected' : ''); ?>>Published</option>
                                    <option value="Private" <?php echo e(in_array('Private', $fareTypes) ? 'selected' : ''); ?>>Private</option>
                                    <option value="Net" <?php echo e(in_array('Net', $fareTypes) ? 'selected' : ''); ?>>Net</option>
                                </select>
                            </div>
                        </div>

                        <!-- Origin Fieldset -->
                        <div class="row">
                            <div class="col-lg-4 mt-3">
                                <label>Origin</label>
                                <fieldset class="p-3 border rounded origin-group">
                                    <?php
                                        $originRuleType = $maskingData['locationGroup']['from']['ruleType'] ?? 'group';
                                        $originValue = $maskingData['locationGroup']['from'][$originRuleType] ?? '';

                                        // For group type, the value should already be the group name
                                        $originGroupName = '';
                                        if ($originRuleType === 'group' && !empty($originValue)) {
                                            $originGroupName = $originValue;
                                        }
                                    ?>

                                    <div class="d-flex mb-2">
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="origin-group-radio-0" name="originRadio-0" value="1" 
                                                class="originRadio form-check-input" onchange="handleOriginRadio(0,1)" 
                                                <?php echo e($originRuleType === 'group' ? 'checked' : ''); ?>>
                                            <label class="mb-0 mx-1 form-label">Group</label>
                                        </div>
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="origin-country-radio-0" name="originRadio-0" value="2" 
                                                class="originRadio form-check-input" onchange="handleOriginRadio(0,2)"
                                                <?php echo e($originRuleType === 'country' ? 'checked' : ''); ?>>
                                            <label class="mb-0 mx-1 form-label">Country</label>
                                        </div>
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="origin-airport-radio-0" name="originRadio-0" value="3" 
                                                class="originRadio form-check-input" onchange="handleOriginRadio(0,3)"
                                                <?php echo e($originRuleType === 'airport' ? 'checked' : ''); ?>>
                                            <label class="mb-0 mx-1 form-label">Airport</label>
                                        </div>
                                    </div>

                                    <!-- Origin Group Container -->
                                    <div class="form-group origin-group-container" id="origin-group-container-0"
                                        style="<?php echo e($originRuleType === 'group' ? '' : 'display:none;'); ?>">
                                        <select name="origin_group-0" class="form-control textBox-style w-100 select2"
                                            placeholder="Enter location group" id="origin-group-0">
                                            <option value="">Select Location Group</option>
                                            <?php if(!empty($location_group)): ?>
                                                <?php $__currentLoopData = $location_group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($item); ?>"
                                                        <?php echo e(($originRuleType === 'group' && $originGroupName === $item) ? 'selected' : ''); ?>>
                                                        <?php echo e($item); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>

                                    <!-- Origin Country Container -->
                                    <div class="form-group origin-country-container" id="origin-country-container-0" 
                                        style="<?php echo e($originRuleType === 'country' ? '' : 'display:none;'); ?>">
                                        <textarea name="origin_country-0" class="form-control textBox-style w-100" 
                                            placeholder="Enter country" id="origin-country-0"><?php if($originRuleType === 'country'): ?><?php echo e($originValue); ?><?php endif; ?></textarea>
                                    </div>

                                    <!-- Origin Airport Container -->
                                    <div class="form-group origin-airport-container" id="origin-airport-container-0" 
                                        style="<?php echo e($originRuleType === 'airport' ? '' : 'display:none;'); ?>">
                                        <textarea name="origin_airport-0" class="form-control textBox-style w-100" 
                                            placeholder="Enter airports" id="origin-airport-0"><?php if($originRuleType === 'airport'): ?><?php echo e($originValue); ?><?php endif; ?></textarea>
                                    </div>
                                </fieldset>
                            </div>

                            <!-- Destination Fieldset -->
                            <div class="col-lg-4 mt-3">
                                <label>Destination</label>
                                <fieldset class="p-3 border rounded destination-group">
                                    <?php
                                        $destinationRuleType = $maskingData['locationGroup']['to']['ruleType'] ?? 'group';
                                        $destinationValue = $maskingData['locationGroup']['to'][$destinationRuleType] ?? '';

                                        // For group type, the value should already be the group name
                                        $destinationGroupName = '';
                                        if ($destinationRuleType === 'group' && !empty($destinationValue)) {
                                            $destinationGroupName = $destinationValue;
                                        }
                                    ?>

                                    <div class="d-flex mb-2">
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="destination-group-radio-0" name="destinationRadio-0" value="4" 
                                                class="destinationRadio form-check-input" onchange="handleDestinationRadio(0,4)" 
                                                <?php echo e($destinationRuleType === 'group' ? 'checked' : ''); ?>>
                                            <label class="mb-0 mx-1 form-label">Group</label>
                                        </div>
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="destination-country-radio-0" name="destinationRadio-0" value="5" 
                                                class="destinationRadio form-check-input" onchange="handleDestinationRadio(0,5)"
                                                <?php echo e($destinationRuleType === 'country' ? 'checked' : ''); ?>>
                                            <label class="mb-0 mx-1 form-label">Country</label>
                                        </div>
                                        <div class="radio-container d-flex pr-3">
                                            <input type="radio" id="destination-airport-radio-0" name="destinationRadio-0" value="6" 
                                                class="destinationRadio form-check-input" onchange="handleDestinationRadio(0,6)"
                                                <?php echo e($destinationRuleType === 'airport' ? 'checked' : ''); ?>>
                                            <label class="mb-0 mx-1 form-label">Airport</label>
                                        </div>
                                    </div>

                                    <!-- Destination Group Container -->
                                    <div class="form-group destination-group-container" id="destination-group-container-0"
                                        style="<?php echo e($destinationRuleType === 'group' ? '' : 'display:none;'); ?>">
                                        <select name="destination_group-0" class="form-control textBox-style w-100 select2"
                                            placeholder="Enter location group" id="destination-group-0">
                                            <option value="">Select Location Group</option>
                                            <?php if(!empty($location_group)): ?>
                                                <?php $__currentLoopData = $location_group; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($item); ?>"
                                                        <?php echo e(($destinationRuleType === 'group' && $destinationGroupName === $item) ? 'selected' : ''); ?>>
                                                        <?php echo e($item); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>

                                    <!-- Destination Country Container -->
                                    <div class="form-group destination-country-container" id="destination-country-container-0" 
                                        style="<?php echo e($destinationRuleType === 'country' ? '' : 'display:none;'); ?>">
                                        <textarea name="destination_country-0" class="form-control textBox-style w-100" 
                                            placeholder="Enter country" id="destination-country-0"><?php if($destinationRuleType === 'country'): ?><?php echo e($destinationValue); ?><?php endif; ?></textarea>
                                    </div>

                                    <!-- Destination Airport Container -->
                                    <div class="form-group destination-airport-container" id="destination-airport-container-0" 
                                        style="<?php echo e($destinationRuleType === 'airport' ? '' : 'display:none;'); ?>">
                                        <textarea name="destination_airport-0" class="form-control textBox-style w-100" 
                                            placeholder="Enter airports" id="destination-airport-0"><?php if($destinationRuleType === 'airport'): ?><?php echo e($destinationValue); ?><?php endif; ?></textarea>
                                    </div>
                                </fieldset>
                            </div>

                            <div class="col-md-2 mt-5">
                                <input type="checkbox" class="form-check-input" name="call_only_fare-0" id="call_only_fare-0"
                                    <?php echo e(($maskingData['callOnlyFares'] ?? false) ? 'checked' : ''); ?>>
                                Call Only Fares
                                <br><br>
                                <input type="checkbox" class="form-check-input" name="booking-0" id="booking-0"
                                    <?php echo e(!empty($maskingData['bookingMode'] ?? null) ? 'checked' : ''); ?>>
                                Booking
                            </div>
                            <div class="col-md-2 mt-5" id="booking_types-0" 
                                style="<?php echo e(!empty($maskingData['bookingMode']) ? '' : 'display:none;'); ?>">
                                <label>Booking Mode</label>
                                <select name="booking_type-0" id="booking_type-0" class="form-control required select2">
                                    <option value="gds" <?php echo e(($maskingData['bookingMode'] ?? '') === 'gds' ? 'selected' : ''); ?>>GDS</option>
                                    <option value="system" <?php echo e(($maskingData['bookingMode'] ?? '') === 'system' ? 'selected' : ''); ?>>SYSTEM</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" name="show_carrier-0" id="show_carrier-0"
                                    <?php echo e(($maskingData['showCarrier'] ?? false) ? 'checked' : ''); ?>>
                                Show Carrier
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" name="mask_validating_carrier-0" id="mask_validating_carrier-0"
                                    <?php echo e(($maskingData['maskValidatingCarrier'] ?? false) ? 'checked' : ''); ?>>
                                Mask Validating Carrier
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" name="show_flight_timing-0" id="show_flight_timing-0"
                                    <?php echo e(($maskingData['showFlightTiming'] ?? false) ? 'checked' : ''); ?>>
                                Show Flight Timing
                            </div>
                            <div class="col-md-3 mt-3">
                                <input type="checkbox" class="form-check-input" name="mask_marketing_carrier-0" id="mask_marketing_carrier-0"
                                    <?php echo e(($maskingData['maskMarketingCarrier'] ?? false) ? 'checked' : ''); ?>>
                                Mask Marketing Carrier
                            </div>
                        
                        </div>

                        <div class="row mt-4">
                            <div class="col-lg-2">
                                <input type="checkbox" class="form-check-input" name="status-0" id="status-0"
                                    <?php echo e($airlineMasking->status ? 'checked' : ''); ?>>
                                Active
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-lg-2">
                        <a href="<?php echo e(route('backend.airline_masking.index')); ?>" class="btn btn-secondary">Back</a>
                    </div>
                    <div class="col-lg-9"></div>
                    <div class="col-lg-1">
                        <button type="submit" class="btn btn-primary">Update</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
    // Initialize select2 elements
    $('.select2').select2();
    $('#content_sources-0').select2();

    // Handle call only fare and booking checkboxes
    const callOnlyFareCheckbox = document.getElementById("call_only_fare-0");
    const bookingCheckbox = document.getElementById("booking-0");
    const bookingTypeContainer = document.getElementById("booking_types-0");

    function toggleBookingTypeVisibility() {
        if (bookingCheckbox.checked) {
            bookingTypeContainer.style.display = "block";
        } else {
            bookingTypeContainer.style.display = "none";
        }
    }

    callOnlyFareCheckbox.addEventListener("change", function() {
        if (this.checked) {
            bookingCheckbox.checked = false;
            toggleBookingTypeVisibility();
        }
    });

    bookingCheckbox.addEventListener("change", function() {
        if (this.checked) {
            callOnlyFareCheckbox.checked = false;
        }
        toggleBookingTypeVisibility();
    });

    // Initialize based on current values
    toggleBookingTypeVisibility();

    // Set up origin and destination based on saved data
    const originRuleType = "<?php echo e($originRuleType); ?>";
    const destinationRuleType = "<?php echo e($destinationRuleType); ?>";
    const originValue = "<?php echo e($originValue); ?>";
    const destinationValue = "<?php echo e($destinationValue); ?>";
    
    // Initialize Tagify for country and airport fields first
    const countryWhitelist = <?php echo json_encode($country); ?>;
    const airportWhitelist = <?php echo json_encode(array_map(function($a) { return $a['name']; }, $airport)); ?>;
    const airportData = <?php echo json_encode($airport); ?>;

    // In the initializeTagify function, modify the country handling part:
    function initializeTagify(element, whitelist, values, isAirport = false) {
        const tagify = new Tagify(element, {
            enforceWhitelist: true,
            whitelist: whitelist,
            dropdown: {
                maxItems: 20,
                classname: "custom-look",
                enabled: 1,
                closeOnSelect: true
            }
        });

        if (values) {
            const valueArray = values.split(',').filter(Boolean);
            if (isAirport) {
                // For airports, we need to map codes to names
                const airportTags = valueArray.map(code => {
                    const airport = airportData.find(a => a.code === code);
                    return airport ? airport.name : code;
                });
                tagify.addTags(airportTags);
            } else {
                // For countries, map codes to "code-name" format
                const countryTags = valueArray.map(code => {
                    // Find the country in whitelist that starts with this code
                    const country = whitelist.find(item => item.startsWith(code + '-'));
                    return country || code;
                });
                tagify.addTags(countryTags);
            }
        }

        return tagify;
    }


    // Handle origin
    if (originRuleType === 'group') {
        document.getElementById('origin-group-radio-0').checked = true;
        document.getElementById('origin-group-container-0').style.display = 'block';
        $(`#origin-group-0`).select2();
    } else if (originRuleType === 'country') {
        document.getElementById('origin-country-radio-0').checked = true;
    document.getElementById('origin-country-container-0').style.display = 'block';
    initializeTagify(document.querySelector('#origin-country-0'), 
        <?php echo json_encode($country); ?>, 
        "<?php echo e($originValue); ?>");
    } else if (originRuleType === 'airport') {
        document.getElementById('origin-airport-radio-0').checked = true;
        document.getElementById('origin-airport-container-0').style.display = 'block';
        initializeTagify(document.querySelector('#origin-airport-0'), airportWhitelist, originValue, true);
    }

    // Handle destination
    if (destinationRuleType === 'group') {
        document.getElementById('destination-group-radio-0').checked = true;
        document.getElementById('destination-group-container-0').style.display = 'block';
        $(`#destination-group-0`).select2();
    } else if (destinationRuleType === 'country') {
        document.getElementById('destination-country-radio-0').checked = true;
    document.getElementById('destination-country-container-0').style.display = 'block';
    initializeTagify(document.querySelector('#destination-country-0'), 
        <?php echo json_encode($country); ?>, 
        "<?php echo e($destinationValue); ?>");
    } else if (destinationRuleType === 'airport') {
        document.getElementById('destination-airport-radio-0').checked = true;
        document.getElementById('destination-airport-container-0').style.display = 'block';
        initializeTagify(document.querySelector('#destination-airport-0'), airportWhitelist, destinationValue, true);
    }
});

// Update your radio handler functions to use the new initializeTagify function
function handleOriginRadio(count, selectedValue) {
    $(`#origin-group-container-${count}, #origin-country-container-${count}, #origin-airport-container-${count}`)
        .hide();

    if (selectedValue == 1) {
        $(`#origin-group-container-${count}`).show();
        $(`#origin-group-${count}`).select2();
    } else if (selectedValue == 2) {
        $(`#origin-country-container-${count}`).show();
        initializeTagify(document.querySelector(`#origin-country-${count}`), 
            <?php echo json_encode($country); ?>, '');
    } else if (selectedValue == 3) {
        $(`#origin-airport-container-${count}`).show();
        initializeTagify(document.querySelector(`#origin-airport-${count}`), 
            <?php echo json_encode(array_map(function($a) { return $a['name']; }, $airport)); ?>, '', true);
    }
}

function handleDestinationRadio(count, selectedValue) {
    $(`#destination-group-container-${count}, #destination-country-container-${count}, #destination-airport-container-${count}`)
        .hide();

    if (selectedValue == 4) {
        $(`#destination-group-container-${count}`).show();
        $(`#destination-group-${count}`).select2();
    } else if (selectedValue == 5) {
        $(`#destination-country-container-${count}`).show();
        initializeTagify(document.querySelector(`#destination-country-${count}`), 
            <?php echo json_encode($country); ?>, '');
    } else if (selectedValue == 6) {
        $(`#destination-airport-container-${count}`).show();
        initializeTagify(document.querySelector(`#destination-airport-${count}`), 
            <?php echo json_encode(array_map(function($a) { return $a['name']; }, $airport)); ?>, '', true);
    }
}
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.layoutMaster', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\crm-ct\crm-ctt\resources\views/backend/airline_masking/edit.blade.php ENDPATH**/ ?>